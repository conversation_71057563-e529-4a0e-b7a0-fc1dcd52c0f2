## Copyright 2023 Databend Cloud
##
## Licensed under the Elastic License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
##     https://www.elastic.co/licensing/elastic-license
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.

statement ok
drop database if exists test_gram_index

statement ok
create database test_gram_index

statement ok
use test_gram_index

statement ok
CREATE TABLE t1 (id int, content string, NGRAM INDEX idx1 (content) gram_size = 5 bloom_size = 1048576)

statement ok
INSERT INTO t1 VALUES
(1, 'The quick brown fox jumps over the lazy dog'),
(2, 'A picture is worth a thousand words'),
(3, 'The early bird catches the worm'),
(4, 'Actions speak louder than words');

query III
select row_count, bloom_filter_size, ngram_index_size from fuse_block('test_gram_index', 't1')
----
4 1049482 1048617

statement ok
CREATE TABLE t2 (id int, content string, name string)

statement error
CREATE NGRAM INDEX idx2 ON t2(content) gram_size = 0

statement error
CREATE NGRAM INDEX idx2 ON t2(content) bloom_size = 0

statement error
CREATE NGRAM INDEX idx2 ON t2(content) bloom_size = 511

statement error
CREATE NGRAM INDEX idx2 ON t2(content) bloom_size = 10485761

statement error
CREATE NGRAM INDEX idx2 ON t2(content, name) gram_size = 5 bloom_size = 1048576

statement ok
CREATE NGRAM INDEX idx2 ON t2(content) gram_size = 5 bloom_size = 1048576

query T
select name, type, original, definition from system.indexes;
----
idx1 NGRAM (empty) t1(content)bloom_size='1048576' gram_size='5'
idx2 NGRAM (empty) t2(content)bloom_size='1048576' gram_size='5'

statement error
CREATE INVERTED INDEX idx1 ON t1(content)

statement ok
DROP NGRAM INDEX idx2 ON t2

query T
select name, type, original, definition from system.indexes;
----
idx1 NGRAM (empty) t1(content)bloom_size='1048576' gram_size='5'

query T
show create table t1;
----
t1 CREATE TABLE t1 (     id INT NULL,     content VARCHAR NULL,     SYNC NGRAM INDEX idx1 (content) bloom_size = '1048576', gram_size = '5'   ) ENGINE=FUSE

statement ok
CREATE OR REPLACE TABLE t1 (id int, content string)

statement ok
INSERT INTO t1 VALUES
(1, 'The quick brown fox jumps over the lazy dog'),
(2, 'A picture is worth a thousand words'),
(3, 'The early bird catches the worm'),
(4, 'Actions speak louder than words');

query II
select block_size, bloom_filter_size, ngram_index_size from fuse_block('test_gram_index', 't1');
----
206 654 NULL

statement ok
CREATE NGRAM INDEX idx1 ON t1(content) gram_size = 5 bloom_size = 1048576

statement ok
REFRESH NGRAM INDEX idx1 ON t1

query II
select block_size, bloom_filter_size, ngram_index_size from fuse_block('test_gram_index', 't1');
----
206 1049482 1048617

statement ok
use default

statement ok
drop database test_gram_index

