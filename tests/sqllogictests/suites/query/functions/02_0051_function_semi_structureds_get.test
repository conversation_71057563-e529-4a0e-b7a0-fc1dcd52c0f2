query T
select get(parse_json('[2.71, 3.14]'), 0)
----
2.71

query T
select get(parse_json('[2.71, 3.14]'), 2)
----
NULL

query T
select get(parse_json('{"aa": 1, "aA": 2, "Aa": 3}'), 'aA')
----
2

query T
select get(parse_json('{"aa": 1, "aA": 2, "Aa": 3}'), 'AA')
----
NULL

query T
select get(parse_json('{"aa": null, "aA": 2, "Aa": 3}'), 'aa')
----
null

query T
select get(null, 'aa')
----
NULL

query T
select parse_json('[2.71, 3.14]')->0
----
2.71

query T
select parse_json('[2.71, 3.14]')->2
----
NULL

query T
select parse_json('[[2.71, 3.14]]')->0->1
----
3.14

query T
select parse_json('{"aa": 1, "aA": 2, "Aa": 3}')->'aA'
----
2

query T
select parse_json('{"aa": 1, "aA": 2, "Aa": 3}')->'AA'
----
NULL

query T
select parse_json('{"aa": null, "aA": 2, "Aa": 3}')->'aa'
----
null

query T
select parse_json(null)->'aa'
----
NULL


query T
select parse_json('[2.71, 3.14]')->>0
----
2.71

query T
select parse_json('[2.71, 3.14]')->>2
----
NULL

query T
select parse_json('{"aa": 1, "aA": "text", "Aa": 3}')->>'aA'
----
text

query T
select parse_json('{"aa": 1, "aA": "text", "Aa": {"a":1}}')->>'Aa'
----
{"a":1}

query T
select parse_json('{"aa": 1, "aA": 2, "Aa": 3}')->>'AA'
----
NULL

query T
select parse_json('{"aa": null, "aA": 2, "Aa": 3}')->>'aa'
----
NULL

query T
select parse_json(null)->>'aa'
----
NULL

query T
select parse_json('{"key1":null}')->>'key1'
----
NULL

query T
select parse_json('[2.71, 3.14]')[0]
----
2.71

query T
select parse_json('[[2.71, 3.14]]')[0][1]
----
3.14

query T
select parse_json('{"aa": 1, "aA": 2, "Aa": 3 }')['aa']
----
1

query T
select '{"k1": 1, "k2": null}'::json['k2']
----
null

query T
select get_ignore_case(parse_json('{"aa": 1, "aA": 2, "Aa": 3}'), 'aA')
----
2

query T
select get_ignore_case(parse_json('{"aa": 1, "aA": 2, "Aa": 3}'), 'AA')
----
3

query T
select get_path(parse_json('{"attr":[{"name": 1}, {"name": 2}]}'), 'attr[0].name')
----
1

query T
select get_path(parse_json('{"attr":[{"name": 1}, {"name": 2}]}'), 'attr[1]["name"]')
----
2

query T
select get_path(parse_json('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}'), 'customer["id"]')
----
1

query T
select get_path(parse_json('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}'), 'customer.name')
----
"databend"

query T
select get_path(parse_json('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}'), 'customer["extras"][0]')
----
"ext"

query T
select get_path(parse_json('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}'), 'customer["extras"][2]')
----
NULL


query T
select get_path(parse_json('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}'), '')
----
{"customer":{"extras":["ext","test"],"id":1,"name":"databend"}}


query T
select json_extract_path_text('{"attr":[{"name": 1}, {"name": 2}]}', 'attr[0].name')
----
1

query T
select json_extract_path_text('{"attr":[{"name": 1}, {"name": 2}]}', 'attr[1]["name"]')
----
2

query T
select json_extract_path_text('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}', 'customer["id"]')
----
1

query T
select json_extract_path_text('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}', 'customer.name')
----
databend

query T
select json_extract_path_text('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}', 'customer["extras"][0]')
----
ext

query T
select json_extract_path_text('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}', 'customer["extras"][2]')
----
NULL

query T
select json_extract_path_text('{"customer":{"id": 1, "name":"databend", "extras":["ext", "test"]}}', '')
----
{"customer":{"extras":["ext","test"],"id":1,"name":"databend"}}

query T
select json_extract_path_text('{"attr":null}', 'attr')
----
NULL

query T
select NULL#>'{0}'
----
NULL

query T
select parse_json('false')#>'{0}'
----
NULL

query T
select parse_json('[1, 2, 3]')#>NULL
----
NULL

query T
select parse_json('[1, 2, 3]')#>'{a}'
----
NULL

query I
select parse_json('[1, 2, 3]')#>'{1}'
----
2

query T
select parse_json('[1, 2, 3]')#>'{3}'
----
NULL

query T
select parse_json('[1, 2, [0, [1, 2], 3]]')#>'{2, 1}'
----
[1,2]

query T
select parse_json('[1, 2, [0, [1, 2], 3]]')#>'{2, 1, 0, 1}'
----
NULL

query T
select parse_json('[1, {"a": 1, "b": 2}, 3]')#>'{1}'
----
{"a":1,"b":2}

query I
select parse_json('[1, {"a": 1, "b": [100, 200, {"k1": 1, "k2": 2}]}, 3]')#>'{1, b, 2, k1}'
----
1

query T
select '[1, 2, 3, null]'::json#>'{3}'
----
null

query T
select NULL#>>'{0}'
----
NULL

query T
select parse_json('false')#>>'{0}'
----
NULL

query T
select parse_json('[1, 2, 3]')#>>'{a}'
----
NULL

query I
select parse_json('[1, 2, 3]')#>>'{1}'
----
2

query T
select parse_json('[1, 2, 3]')#>>'{3}'
----
NULL

query T
select parse_json('[1, 2, [0, [1, 2], 3]]')#>>'{2, 1}'
----
[1,2]

query T
select parse_json('[1, 2, [0, [1, 2], 3]]')#>>'{2, 1, 0, 1}'
----
NULL

query T
select parse_json('[1, {"a": 1, "b": 2}, 3]')#>>'{1}'
----
{"a":1,"b":2}

query I
select parse_json('[1, {"a": 1, "b": [100, 200, {"k1": 1, "k2": 2}]}, 3]')#>>'{1, b, 2, k1}'
----
1

query T
select '[1, 2, 3, null]'::json#>>'{3}'
----
NULL

query T
select [][0]
----
NULL

query I
select [0,1,2,3][1]
----
0

query T
select [[[1,2,3],[4,5,6]],[[7,8,9]]][1]
----
[[1,2,3],[4,5,6]]

query T
select [[[1,2,3],[4,5,6]],[[7,8,9]]][1][2]
----
[4,5,6]

query I
select [[[1,2,3],[4,5,6]],[[7,8,9]]][1][2][3]
----
6

query I
select ([0,1,2,3])[1]
----
0

statement ok
DROP DATABASE IF EXISTS db1

statement ok
CREATE DATABASE db1

statement ok
USE db1

statement ok
CREATE TABLE IF NOT EXISTS t1(id Int null, arr VARIANT null) Engine = Fuse

statement ok
insert into t1 select 1, parse_json('[1,2,3,["a","b","c"]]')

statement ok
CREATE TABLE IF NOT EXISTS t2(id Int null, obj Variant null) Engine = Fuse

statement ok
insert into t2 select 1, parse_json('{"a": 1,"b":{"c": 2}}')

statement ok
CREATE TABLE IF NOT EXISTS t3(id Int null, str String null) Engine = Fuse

statement ok
insert into t3 values(1, '[1,2,3,["a","b","c"]]'), (2, '{"a": 1,"b":{"c": 2}}')

statement ok
CREATE TABLE IF NOT EXISTS t4(id Int null, arr Array(Int64)) Engine = Fuse

statement ok
insert into t4 values(1, [10,20,30,40]), (2, [50,60,70,80])

statement ok
CREATE TABLE IF NOT EXISTS t5(id Int null, obj Variant null) Engine = Fuse

statement ok
insert into t5 values(1, '{"car_no":10,"测试\\"\\uD83D\\uDC8E":"a","nums":[2,3,4],"obj":{"a":{"b":[1,2]},"c":1}}'), (2, '{"car_no":20,"nums":[-10,-11],"obj":{"x":"y"}}')

query T
select get(arr, 0) from t1
----
1

query T
select get(arr, 'a') from t1
----
NULL

query T
select get(obj, 0) from t2
----
NULL

query T
select get(obj, 'a') from t2
----
1

query T
select arr->0 from t1
----
1

query T
select arr->'a' from t1
----
NULL

query T
select obj->0 from t2
----
NULL

query T
select obj->'a' from t2
----
1

query T
select get_ignore_case(obj, 'a') from t2
----
1

query T
select get_ignore_case(obj, 'A') from t2
----
1

query IT
select id, obj:b from (select id, obj from t2) tt
----
1 {"c":2}

query IT
select id, obj:b from (select id, obj from t2)
----
1 {"c":2}

query T
select get_path(arr, '[0]') from t1
----
1

query T
select get_path(arr, '[3][0]') from t1
----
"a"

query T
select get_path(obj, 'a') from t2
----
1

query T
select get_path(obj, '["a"]') from t2
----
1

query T
select get_path(obj, 'b.c') from t2
----
2

query T
select get_path(obj, '["b"]["c"]') from t2
----
2

query IT
select id, json_extract_path_text(str, '[0]') from t3
----
1 1
2 NULL

query IT
select id, json_extract_path_text(str, '[3][0]') from t3
----
1 a
2 NULL

query IT
select id, json_extract_path_text(str, 'a') from t3
----
1 NULL
2 1

query IT
select id, json_extract_path_text(str, '["a"]') from t3
----
1 NULL
2 1

query IT
select id, json_extract_path_text(str, 'b.c') from t3
----
1 NULL
2 2

query IT
select id, json_extract_path_text(str, '["b"]["c"]') from t3
----
1 NULL
2 2

query II
select id, get(arr, 1) from t4
----
1 10
2 50

query II
select id, get(arr, 2) from t4
----
1 20
2 60

query II
select id, get(arr, 5) from t4
----
1 NULL
2 NULL


statement error 1006
select id, get(arr, 'a') from t4

statement ok
set max_block_size = 2;

query IT?
select id, json_path_query(arr, '$[2, 1 to last -1]'), typeof(json_path_query(arr, '$[2, 1 to last -1]')) from t1
----
1 3 number
1 2 number
1 3 number

query IT
select id, json_path_query(arr, '$[*]?(@ > 1 && @ <= 3)') from t1
----
1 2
1 3

query IT
select id, json_path_query(arr, '$[3][*]?(@ starts with "a")') from t1
----
1 "a"

query IT
select id, json_path_query(arr, '$[*][1]') from t1
----
1 "b"

query IT
select id, json_path_query(obj, '$.a') from t2
----
1 1

query IT
select id, json_path_query(obj, '$.a') from t2
----
1 1

query IT
select id, json_path_query(obj, '$.b.c') from t2
----
1 2

query IT
select id, json_path_query(obj, '$.b?(@.c == 2)') from t2
----
1 {"c":2}

query IT
select id, json_path_query(obj, '$.b?(@.c > 2)') from t2
----

statement error 1006
select id, json_path_query(obj, '--') from t2

statement error 1065
select id from t2 where json_path_query(obj, '$.a') = 1

statement error 1065
select id from t2 having json_path_query(obj, '$.a') = 1

query IT
select id, json_path_query_array(arr, '$[2, 1 to last -1]') from t1
----
1 [3,2,3]

query IT
select id, json_path_query_array(arr, '$[*]?(@ > 1 && @ <= 3)') from t1
----
1 [2,3]

query IT
select id, json_path_query_array(arr, '$[*][1]') from t1
----
1 ["b"]

query IT
select id, json_path_query_array(obj, '$.a') from t2
----
1 [1]

query IT
select id, json_path_query_array(obj, '$.b.c') from t2
----
1 [2]

query IT
select id, json_path_query_array(obj, '$.b?(@.c == 2)') from t2
----
1 [{"c":2}]

query IT
select id, json_path_query_array(obj, '$.b?(@.c > 2)') from t2
----
1 []

statement error 1006
select id, json_path_query_array(obj, '--') from t2

query IT
select id, json_path_query_first(arr, '$[2, 1 to last -1]') from t1
----
1 3

query IT
select id, json_path_query_first(arr, '$[*]?(@ > 1 && @ <= 3)') from t1
----
1 2

query IT
select id, json_path_query_first(arr, '$[*][1]') from t1
----
1 "b"

query IT
select id, json_path_query_first(obj, '$.a') from t2
----
1 1

query IT
select id, json_path_query_first(obj, '$.b.c') from t2
----
1 2

query IT
select id, json_path_query_first(obj, '$.b?(@.c == 2)') from t2
----
1 {"c":2}

query IT
select id, json_path_query_first(obj, '$.b?(@.c > 2)') from t2
----
1 NULL

statement error 1006
select id, json_path_query_first(obj, '--') from t2

query T
select get(obj, 'car_no') from t5
----
10
20

query T
select get_path(obj, '["car_no"]') from t5
----
10
20

query T
select get(obj, '测试"💎') from t5
----
"a"
NULL

query T
select get_path(obj, '["测试\\"\\uD83D\\uDC8E"]') from t5
----
"a"
NULL

query T
select json_path_query(obj, '$.测试\\"\\uD83D\\uDC8E') from t5
----
"a"

query T
select json_path_query_array(obj, '$.测试\\"\\uD83D\\uDC8E') from t5
----
["a"]
[]

query T
select json_path_query_first(obj, '$.测试\\"\\uD83D\\uDC8E') from t5
----
"a"
NULL

query IT
select id, json_path_query(obj, '$.obj.**') from t5
----
1 {"a":{"b":[1,2]},"c":1}
1 {"b":[1,2]}
1 [1,2]
1 1
1 2
1 1
2 {"x":"y"}
2 "y"

query IT
select id, json_path_query(obj, '$.obj.**{2 to last}') from t5
----
1 [1,2]
1 1
1 2

query IT
select id, json_path_query(obj, '$.obj.**{1}') from t5
----
1 {"b":[1,2]}
1 1
2 "y"

query IT
select id, json_path_query(obj, '$.obj.**{1}.b') from t5
----
1 [1,2]

query IT
select id, json_path_query(obj, '+$.nums') from t5
----
1 2
1 3
1 4
2 -10
2 -11

query IT
select id, json_path_query(obj, '-$.nums') from t5
----
1 -2
1 -3
1 -4
2 10
2 11

query IT
select id, json_path_query(obj, '$.nums[0] + 3') from t5
----
1 5
2 -7

query IT
select id, json_path_query(obj, '7 - $.nums[0]') from t5
----
1 5
2 17

query IT
select id, json_path_query(obj, '2 * $.nums[1]') from t5
----
1 6
2 -22

query IT
select id, json_path_query(obj, '$.nums[1] / 2') from t5
----
1 1.5
2 -5.5

query IT
select id, json_path_query(obj, '$.nums[1] % 2') from t5
----
1 1
2 -1

statement ok
set max_block_size = 65535;

query T
SELECT arr #> '{3}' FROM t1;
----
["a","b","c"]

query T
SELECT arr #> '{3,1}' FROM t1;
----
"b"

query I
SELECT obj #> '{a}' FROM t2;
----
1

query T
SELECT arr #>> '{3}' FROM t1;
----
["a","b","c"]

query T
SELECT arr #>> '{3,1}' FROM t1;
----
b

query I
SELECT obj #>> '{a}' FROM t2;
----
1

statement ok
DROP DATABASE IF EXISTS db1
