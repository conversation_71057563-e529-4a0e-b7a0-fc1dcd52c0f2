// Copyright 2021 Datafuse Labs
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

use std::borrow::Cow;
use std::collections::BTreeMap;
use std::collections::HashMap;
use std::sync::Arc;

use databend_common_catalog::plan::Projection;
use databend_common_catalog::table_context::TableContext;
use databend_common_exception::Result;
use databend_common_expression::BlockEntry;
use databend_common_expression::ColumnId;
use databend_common_expression::DataBlock;
use databend_common_expression::FieldIndex;
use databend_common_expression::TableDataType;
use databend_common_expression::TableField;
use databend_common_expression::TableSchema;
use databend_common_expression::TableSchemaRef;
use databend_common_io::constants::DEFAULT_BLOCK_INDEX_BUFFER_SIZE;
use databend_storages_common_blocks::blocks_to_parquet;
use databend_storages_common_index::filters::BlockFilter;
use databend_storages_common_index::BloomIndex;
use databend_storages_common_index::BloomIndexBuilder;
use databend_storages_common_index::NgramArgs;
use databend_storages_common_io::ReadSettings;
use databend_storages_common_table_meta::meta::column_oriented_segment::BlockReadInfo;
use databend_storages_common_table_meta::meta::Location;
use databend_storages_common_table_meta::meta::Versioned;
use databend_storages_common_table_meta::table::TableCompression;
use opendal::Operator;

use crate::io::BlockReader;
use crate::FuseStorageFormat;

pub struct NewNgramIndexColumn {
    column_id: ColumnId,
    column: BlockEntry,
    ngram_args: NgramArgs,
}

impl NewNgramIndexColumn {
    pub fn new(column_id: ColumnId, column: BlockEntry, ngram_args: NgramArgs) -> Self {
        Self {
            column_id,
            column,
            ngram_args,
        }
    }
}

pub struct BloomIndexState {
    pub(crate) data: Vec<u8>,
    pub(crate) size: u64,
    pub(crate) ngram_size: Option<u64>,
    pub(crate) location: Location,
    pub(crate) column_distinct_count: HashMap<ColumnId, usize>,
}

impl BloomIndexState {
    pub fn from_bloom_index(
        bloom_index: &BloomIndex,
        location: Location,
        new_index_column: Option<NewNgramIndexColumn>,
    ) -> Result<Self> {
        let mut index_block = bloom_index.serialize_to_data_block()?;
        let filter_schema = if let Some(NewNgramIndexColumn {
            column_id,
            column: new_column,
            ngram_args,
        }) = new_index_column
        {
            let ngram_name = BloomIndex::build_filter_ngram_name(
                column_id,
                ngram_args.gram_size(),
                ngram_args.bloom_size(),
            );
            let mut new_filter_schema = TableSchema::clone(&bloom_index.filter_schema);
            let ngram_field = TableField::new(&ngram_name, TableDataType::Binary);

            if let Some(pos) = new_filter_schema
                .fields()
                .iter()
                .position(|f| f.name().starts_with(format!("Ngram({column_id})").as_str()))
            {
                new_filter_schema.fields.remove(pos);
                index_block.remove_column(pos);
            }
            new_filter_schema.add_columns(&[ngram_field])?;
            index_block.add_entry(new_column);
            Cow::Owned(Arc::new(new_filter_schema))
        } else {
            Cow::Borrowed(&bloom_index.filter_schema)
        };
        // Calculate ngram index size
        let ngram_indexes = filter_schema
            .fields()
            .iter()
            .enumerate()
            .filter(|(_, f)| f.name.starts_with("Ngram"))
            .map(|(i, _)| i)
            .collect::<Vec<_>>();
        let ngram_size = if !ngram_indexes.is_empty() {
            let mut ngram_size = 0;
            for i in ngram_indexes {
                let column = index_block.get_by_offset(i);
                ngram_size += column.value().memory_size() as u64;
            }
            Some(ngram_size)
        } else {
            None
        };
        let mut data = Vec::with_capacity(DEFAULT_BLOCK_INDEX_BUFFER_SIZE);
        let _ = blocks_to_parquet(
            &filter_schema,
            vec![index_block],
            &mut data,
            TableCompression::None,
        )?;
        let data_size = data.len() as u64;
        Ok(Self {
            data,
            size: data_size,
            ngram_size,
            location,
            column_distinct_count: bloom_index.column_distinct_count.clone(),
        })
    }

    pub fn from_data_block(
        ctx: Arc<dyn TableContext>,
        block: &DataBlock,
        location: Location,
        bloom_columns_map: BTreeMap<FieldIndex, TableField>,
        ngram_args: &[NgramArgs],
    ) -> Result<Option<Self>> {
        // write index
        let mut builder =
            BloomIndexBuilder::create(ctx.get_function_context()?, bloom_columns_map, ngram_args)?;
        builder.add_block(block)?;
        let maybe_bloom_index = builder.finalize()?;
        if let Some(bloom_index) = maybe_bloom_index {
            Ok(Some(Self::from_bloom_index(&bloom_index, location, None)?))
        } else {
            Ok(None)
        }
    }

    pub fn size(&self) -> u64 {
        self.size
    }

    pub fn data(self) -> Vec<u8> {
        self.data
    }

    pub fn ngram_size(&self) -> Option<u64> {
        self.ngram_size
    }
}

#[derive(Clone)]
pub struct BloomIndexRebuilder {
    pub table_ctx: Arc<dyn TableContext>,
    pub table_schema: TableSchemaRef,
    pub table_dal: Operator,
    pub storage_format: FuseStorageFormat,
    pub bloom_columns_map: BTreeMap<FieldIndex, TableField>,
    pub ngram_args: Vec<NgramArgs>,
}

impl BloomIndexRebuilder {
    pub async fn bloom_index_state_from_block_meta(
        &self,
        bloom_index_location: &Location,
        block_read_info: &BlockReadInfo,
    ) -> Result<Option<(BloomIndexState, BloomIndex)>> {
        let ctx = self.table_ctx.clone();

        let projection =
            Projection::Columns((0..self.table_schema.fields().len()).collect::<Vec<usize>>());

        let block_reader = BlockReader::create(
            ctx,
            self.table_dal.clone(),
            self.table_schema.clone(),
            projection,
            false,
            false,
            false,
        )?;

        let settings = ReadSettings::from_ctx(&self.table_ctx)?;

        let merge_io_read_result = block_reader
            .read_columns_data_by_merge_io(
                &settings,
                &block_read_info.location,
                &block_read_info.col_metas,
                &None,
            )
            .await?;
        let data_block = block_reader.deserialize_chunks_with_meta(
            block_read_info,
            &self.storage_format,
            merge_io_read_result,
        )?;

        assert_eq!(bloom_index_location.1, BlockFilter::VERSION);
        let mut builder = BloomIndexBuilder::create(
            self.table_ctx.get_function_context()?,
            self.bloom_columns_map.clone(),
            &self.ngram_args,
        )?;
        builder.add_block(&data_block)?;
        let maybe_bloom_index = builder.finalize()?;

        match maybe_bloom_index {
            None => Ok(None),
            Some(bloom_index) => Ok(Some((
                BloomIndexState::from_bloom_index(
                    &bloom_index,
                    bloom_index_location.clone(),
                    None,
                )?,
                bloom_index,
            ))),
        }
    }
}
