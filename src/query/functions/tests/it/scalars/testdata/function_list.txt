Function aliases (alias to origin):
add -> plus
array_contains -> contains
array_get -> get
array_length -> length
array_size -> length
array_slice -> slice
between_dows -> between_days
between_doys -> between_days
between_epochs -> between_seconds
between_isodows -> between_days
between_yearweeks -> between_weeks
bitmap_and_not -> bitmap_not
bitmap_cardinality -> bitmap_count
ceiling -> ceil
char_length -> length
character_length -> length
chr -> char
current_timestamp -> now
date -> to_date
date_format -> to_string
day -> to_day_of_month
dayofmonth -> to_day_of_month
dayofyear -> to_day_of_year
diff_dows -> diff_days
diff_doys -> diff_days
diff_epochs -> diff_seconds
diff_isodows -> diff_days
hex -> to_hex
intdiv -> div
ipv4_num_to_string -> inet_ntoa
ipv4_string_to_num -> inet_aton
json_to_string -> to_string
lcase -> lower
length_utf8 -> length
mid -> substr
mod -> modulo
month -> to_month
neg -> minus
negate -> minus
object_keys -> json_object_keys
power -> pow
quarter -> to_quarter
remove_nullable -> assume_not_null
rlike -> regexp
sha1 -> sha
siphash -> siphash64
st_asbinary -> st_aswkb
st_astext -> st_aswkt
st_geogfromewkt -> st_geographyfromewkt
st_geogfromtext -> st_geographyfromewkt
st_geogfromwkt -> st_geographyfromewkt
st_geographyfromtext -> st_geographyfromewkt
st_geographyfromwkt -> st_geographyfromewkt
st_geom_point -> st_makegeompoint
st_geometryfromewkb -> st_geometryfromwkb
st_geometryfromewkt -> st_geometryfromwkt
st_geometryfromtext -> st_geometryfromwkt
st_geomfromewkb -> st_geometryfromwkb
st_geomfromewkt -> st_geometryfromwkt
st_geomfromtext -> st_geometryfromwkt
st_geomfromwkb -> st_geometryfromwkb
st_geomfromwkt -> st_geometryfromwkt
st_make_line -> st_makeline
st_numpoints -> st_npoints
st_point -> st_makepoint
st_polygon -> st_makepolygon
str_to_date -> to_date
str_to_timestamp -> to_timestamp
str_to_year -> to_year
strftime -> to_string
substr_utf8 -> substr
substring -> substr
substring_utf8 -> substr
subtract -> minus
to_char -> to_string
to_datetime -> to_timestamp
to_start_of_iso_week -> to_monday
to_text -> to_string
to_varchar -> to_string
trunc -> truncate
try_ipv4_num_to_string -> try_inet_ntoa
try_ipv4_string_to_num -> try_inet_aton
try_to_datetime -> try_to_timestamp
ucase -> upper
unhex -> from_hex
uuid -> gen_random_uuid
week -> to_week_of_year
weekofyear -> to_week_of_year
year -> to_year

Functions overloads:
0 abs FACTORY
1 abs(UInt64) :: UInt64
2 abs(UInt64 NULL) :: UInt64 NULL
3 abs(Int64) :: UInt64
4 abs(Int64 NULL) :: UInt64 NULL
5 abs(Float64) :: Float64
6 abs(Float64 NULL) :: Float64 NULL
0 acos(Float64) :: Float64
1 acos(Float64 NULL) :: Float64 NULL
0 add_days(Date, Int64) :: Date
1 add_days(Date NULL, Int64 NULL) :: Date NULL
2 add_days(Timestamp, Int64) :: Timestamp
3 add_days(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 add_hours(Date, Int64) :: Timestamp
1 add_hours(Date NULL, Int64 NULL) :: Timestamp NULL
2 add_hours(Timestamp, Int64) :: Timestamp
3 add_hours(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 add_minutes(Date, Int64) :: Timestamp
1 add_minutes(Date NULL, Int64 NULL) :: Timestamp NULL
2 add_minutes(Timestamp, Int64) :: Timestamp
3 add_minutes(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 add_months(Date, Int64) :: Date
1 add_months(Date NULL, Int64 NULL) :: Date NULL
2 add_months(Timestamp, Int64) :: Timestamp
3 add_months(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 add_quarters(Date, Int64) :: Date
1 add_quarters(Date NULL, Int64 NULL) :: Date NULL
2 add_quarters(Timestamp, Int64) :: Timestamp
3 add_quarters(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 add_seconds(Date, Int64) :: Timestamp
1 add_seconds(Date NULL, Int64 NULL) :: Timestamp NULL
2 add_seconds(Timestamp, Int64) :: Timestamp
3 add_seconds(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 add_weeks(Date, Int64) :: Date
1 add_weeks(Date NULL, Int64 NULL) :: Date NULL
2 add_weeks(Timestamp, Int64) :: Timestamp
3 add_weeks(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 add_years(Date, Int64) :: Date
1 add_years(Date NULL, Int64 NULL) :: Date NULL
2 add_years(Timestamp, Int64) :: Timestamp
3 add_years(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 age(Timestamp, Timestamp) :: Interval
1 age(Timestamp NULL, Timestamp NULL) :: Interval NULL
2 age(Timestamp) :: Interval
3 age(Timestamp NULL) :: Interval NULL
0 ai_embedding_vector(String) :: Array(Float32)
1 ai_embedding_vector(String NULL) :: Array(Float32) NULL
0 ai_text_completion(String) :: String
1 ai_text_completion(String NULL) :: String NULL
0 and(Boolean, Boolean) :: Boolean
1 and(Boolean NULL, Boolean NULL) :: Boolean NULL
0 and_filters FACTORY
0 array() :: Array(Nothing)
1 array FACTORY
0 array_any FACTORY
0 array_append(Array(T0) NULL, T0) :: Array(T0)
0 array_approx_count_distinct FACTORY
0 array_avg FACTORY
0 array_concat(Array(Nothing), Array(Nothing)) :: Array(Nothing)
1 array_concat(Array(Nothing) NULL, Array(Nothing) NULL) :: Array(Nothing) NULL
2 array_concat(Array(T0), Array(T0)) :: Array(T0)
3 array_concat(Array(T0) NULL, Array(T0) NULL) :: Array(T0) NULL
0 array_count FACTORY
0 array_distinct(Array(Nothing)) :: Array(Nothing)
1 array_distinct(Array(Nothing) NULL) :: Array(Nothing) NULL
2 array_distinct(Array(T0)) :: Array(T0)
3 array_distinct(Array(T0) NULL) :: Array(T0) NULL
0 array_flatten(Array(Array(T0))) :: Array(T0)
1 array_flatten(Array(Array(T0)) NULL) :: Array(T0) NULL
0 array_indexof(NULL, NULL) :: NULL
1 array_indexof(Array(T0), T0) :: UInt64
2 array_indexof(Array(T0) NULL, T0 NULL) :: UInt64 NULL
0 array_intersection(Array(T0), Array(T0)) :: Array(T0)
1 array_intersection(Array(T0) NULL, Array(T0) NULL) :: Array(T0) NULL
0 array_kurtosis FACTORY
0 array_max FACTORY
0 array_median FACTORY
0 array_min FACTORY
0 array_prepend(T0, Array(T0) NULL) :: Array(T0)
0 array_remove_first(Array(Nothing)) :: Array(Nothing)
1 array_remove_first(Array(Nothing) NULL) :: Array(Nothing) NULL
2 array_remove_first(Array(T0)) :: Array(T0)
3 array_remove_first(Array(T0) NULL) :: Array(T0) NULL
0 array_remove_last(Array(Nothing)) :: Array(Nothing)
1 array_remove_last(Array(Nothing) NULL) :: Array(Nothing) NULL
2 array_remove_last(Array(T0)) :: Array(T0)
3 array_remove_last(Array(T0) NULL) :: Array(T0) NULL
0 array_skewness FACTORY
0 array_sort_asc_null_first(Array(Nothing)) :: Array(Nothing)
1 array_sort_asc_null_first(Array(Nothing) NULL) :: Array(Nothing) NULL
2 array_sort_asc_null_first(Array(T0)) :: Array(T0)
3 array_sort_asc_null_first(Array(T0) NULL) :: Array(T0) NULL
0 array_sort_asc_null_last(Array(Nothing)) :: Array(Nothing)
1 array_sort_asc_null_last(Array(Nothing) NULL) :: Array(Nothing) NULL
2 array_sort_asc_null_last(Array(T0)) :: Array(T0)
3 array_sort_asc_null_last(Array(T0) NULL) :: Array(T0) NULL
0 array_sort_desc_null_first(Array(Nothing)) :: Array(Nothing)
1 array_sort_desc_null_first(Array(Nothing) NULL) :: Array(Nothing) NULL
2 array_sort_desc_null_first(Array(T0)) :: Array(T0)
3 array_sort_desc_null_first(Array(T0) NULL) :: Array(T0) NULL
0 array_sort_desc_null_last(Array(Nothing)) :: Array(Nothing)
1 array_sort_desc_null_last(Array(Nothing) NULL) :: Array(Nothing) NULL
2 array_sort_desc_null_last(Array(T0)) :: Array(T0)
3 array_sort_desc_null_last(Array(T0) NULL) :: Array(T0) NULL
0 array_std FACTORY
0 array_stddev FACTORY
0 array_stddev_pop FACTORY
0 array_stddev_samp FACTORY
0 array_sum FACTORY
0 array_to_string(Array(String), String) :: String
1 array_to_string(Array(String) NULL, String NULL) :: String NULL
2 array_to_string(Array(String NULL), String) :: String
3 array_to_string(Array(String NULL) NULL, String NULL) :: String NULL
0 array_unique(Array(Nothing)) :: UInt64
1 array_unique(Array(Nothing) NULL) :: UInt64 NULL
2 array_unique(Array(T0)) :: UInt64
3 array_unique(Array(T0) NULL) :: UInt64 NULL
0 arrays_zip FACTORY
0 as_array(Variant) :: Variant NULL
1 as_array(Variant NULL) :: Variant NULL
0 as_binary(Variant) :: Binary NULL
1 as_binary(Variant NULL) :: Binary NULL
0 as_boolean(Variant) :: Boolean NULL
1 as_boolean(Variant NULL) :: Boolean NULL
0 as_date(Variant) :: Date NULL
1 as_date(Variant NULL) :: Date NULL
0 as_decimal FACTORY
0 as_float(Variant) :: Float64 NULL
1 as_float(Variant NULL) :: Float64 NULL
0 as_integer(Variant) :: Int64 NULL
1 as_integer(Variant NULL) :: Int64 NULL
0 as_interval(Variant) :: Interval NULL
1 as_interval(Variant NULL) :: Interval NULL
0 as_object(Variant) :: Variant NULL
1 as_object(Variant NULL) :: Variant NULL
0 as_string(Variant) :: String NULL
1 as_string(Variant NULL) :: String NULL
0 as_timestamp(Variant) :: Timestamp NULL
1 as_timestamp(Variant NULL) :: Timestamp NULL
0 ascii(String) :: UInt8
1 ascii(String NULL) :: UInt8 NULL
0 asin(Float64) :: Float64
1 asin(Float64 NULL) :: Float64 NULL
0 assume_not_null(T0 NULL) :: T0
0 atan(Float64) :: Float64
1 atan(Float64 NULL) :: Float64 NULL
0 atan2(Float64, Float64) :: Float64
1 atan2(Float64 NULL, Float64 NULL) :: Float64 NULL
0 between_days(Date, Date) :: Int64
1 between_days(Date NULL, Date NULL) :: Int64 NULL
2 between_days(Timestamp, Timestamp) :: Int64
3 between_days(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_hours(Date, Date) :: Int64
1 between_hours(Date NULL, Date NULL) :: Int64 NULL
2 between_hours(Timestamp, Timestamp) :: Int64
3 between_hours(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_isoyears(Date, Date) :: Int64
1 between_isoyears(Date NULL, Date NULL) :: Int64 NULL
2 between_isoyears(Timestamp, Timestamp) :: Int64
3 between_isoyears(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_microseconds(Date, Date) :: Int64
1 between_microseconds(Date NULL, Date NULL) :: Int64 NULL
2 between_microseconds(Timestamp, Timestamp) :: Int64
3 between_microseconds(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_millenniums(Date, Date) :: Int64
1 between_millenniums(Date NULL, Date NULL) :: Int64 NULL
2 between_millenniums(Timestamp, Timestamp) :: Int64
3 between_millenniums(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_minutes(Date, Date) :: Int64
1 between_minutes(Date NULL, Date NULL) :: Int64 NULL
2 between_minutes(Timestamp, Timestamp) :: Int64
3 between_minutes(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_months(Date, Date) :: Int64
1 between_months(Date NULL, Date NULL) :: Int64 NULL
2 between_months(Timestamp, Timestamp) :: Int64
3 between_months(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_quarters(Date, Date) :: Int64
1 between_quarters(Date NULL, Date NULL) :: Int64 NULL
2 between_quarters(Timestamp, Timestamp) :: Int64
3 between_quarters(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_seconds(Date, Date) :: Int64
1 between_seconds(Date NULL, Date NULL) :: Int64 NULL
2 between_seconds(Timestamp, Timestamp) :: Int64
3 between_seconds(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_weeks(Date, Date) :: Int64
1 between_weeks(Date NULL, Date NULL) :: Int64 NULL
2 between_weeks(Timestamp, Timestamp) :: Int64
3 between_weeks(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 between_years(Date, Date) :: Int64
1 between_years(Date NULL, Date NULL) :: Int64 NULL
2 between_years(Timestamp, Timestamp) :: Int64
3 between_years(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 bin(Int64) :: String
1 bin(Int64 NULL) :: String NULL
0 bit_and(UInt8, UInt8) :: Int64
1 bit_and(UInt8 NULL, UInt8 NULL) :: Int64 NULL
2 bit_and(UInt8, UInt16) :: Int64
3 bit_and(UInt8 NULL, UInt16 NULL) :: Int64 NULL
4 bit_and(UInt8, UInt32) :: Int64
5 bit_and(UInt8 NULL, UInt32 NULL) :: Int64 NULL
6 bit_and(UInt8, UInt64) :: Int64
7 bit_and(UInt8 NULL, UInt64 NULL) :: Int64 NULL
8 bit_and(UInt8, Int8) :: Int64
9 bit_and(UInt8 NULL, Int8 NULL) :: Int64 NULL
10 bit_and(UInt8, Int16) :: Int64
11 bit_and(UInt8 NULL, Int16 NULL) :: Int64 NULL
12 bit_and(UInt8, Int32) :: Int64
13 bit_and(UInt8 NULL, Int32 NULL) :: Int64 NULL
14 bit_and(UInt8, Int64) :: Int64
15 bit_and(UInt8 NULL, Int64 NULL) :: Int64 NULL
16 bit_and(UInt16, UInt8) :: Int64
17 bit_and(UInt16 NULL, UInt8 NULL) :: Int64 NULL
18 bit_and(UInt16, UInt16) :: Int64
19 bit_and(UInt16 NULL, UInt16 NULL) :: Int64 NULL
20 bit_and(UInt16, UInt32) :: Int64
21 bit_and(UInt16 NULL, UInt32 NULL) :: Int64 NULL
22 bit_and(UInt16, UInt64) :: Int64
23 bit_and(UInt16 NULL, UInt64 NULL) :: Int64 NULL
24 bit_and(UInt16, Int8) :: Int64
25 bit_and(UInt16 NULL, Int8 NULL) :: Int64 NULL
26 bit_and(UInt16, Int16) :: Int64
27 bit_and(UInt16 NULL, Int16 NULL) :: Int64 NULL
28 bit_and(UInt16, Int32) :: Int64
29 bit_and(UInt16 NULL, Int32 NULL) :: Int64 NULL
30 bit_and(UInt16, Int64) :: Int64
31 bit_and(UInt16 NULL, Int64 NULL) :: Int64 NULL
32 bit_and(UInt32, UInt8) :: Int64
33 bit_and(UInt32 NULL, UInt8 NULL) :: Int64 NULL
34 bit_and(UInt32, UInt16) :: Int64
35 bit_and(UInt32 NULL, UInt16 NULL) :: Int64 NULL
36 bit_and(UInt32, UInt32) :: Int64
37 bit_and(UInt32 NULL, UInt32 NULL) :: Int64 NULL
38 bit_and(UInt32, UInt64) :: Int64
39 bit_and(UInt32 NULL, UInt64 NULL) :: Int64 NULL
40 bit_and(UInt32, Int8) :: Int64
41 bit_and(UInt32 NULL, Int8 NULL) :: Int64 NULL
42 bit_and(UInt32, Int16) :: Int64
43 bit_and(UInt32 NULL, Int16 NULL) :: Int64 NULL
44 bit_and(UInt32, Int32) :: Int64
45 bit_and(UInt32 NULL, Int32 NULL) :: Int64 NULL
46 bit_and(UInt32, Int64) :: Int64
47 bit_and(UInt32 NULL, Int64 NULL) :: Int64 NULL
48 bit_and(UInt64, UInt8) :: Int64
49 bit_and(UInt64 NULL, UInt8 NULL) :: Int64 NULL
50 bit_and(UInt64, UInt16) :: Int64
51 bit_and(UInt64 NULL, UInt16 NULL) :: Int64 NULL
52 bit_and(UInt64, UInt32) :: Int64
53 bit_and(UInt64 NULL, UInt32 NULL) :: Int64 NULL
54 bit_and(UInt64, UInt64) :: Int64
55 bit_and(UInt64 NULL, UInt64 NULL) :: Int64 NULL
56 bit_and(UInt64, Int8) :: Int64
57 bit_and(UInt64 NULL, Int8 NULL) :: Int64 NULL
58 bit_and(UInt64, Int16) :: Int64
59 bit_and(UInt64 NULL, Int16 NULL) :: Int64 NULL
60 bit_and(UInt64, Int32) :: Int64
61 bit_and(UInt64 NULL, Int32 NULL) :: Int64 NULL
62 bit_and(UInt64, Int64) :: Int64
63 bit_and(UInt64 NULL, Int64 NULL) :: Int64 NULL
64 bit_and(Int8, UInt8) :: Int64
65 bit_and(Int8 NULL, UInt8 NULL) :: Int64 NULL
66 bit_and(Int8, UInt16) :: Int64
67 bit_and(Int8 NULL, UInt16 NULL) :: Int64 NULL
68 bit_and(Int8, UInt32) :: Int64
69 bit_and(Int8 NULL, UInt32 NULL) :: Int64 NULL
70 bit_and(Int8, UInt64) :: Int64
71 bit_and(Int8 NULL, UInt64 NULL) :: Int64 NULL
72 bit_and(Int8, Int8) :: Int64
73 bit_and(Int8 NULL, Int8 NULL) :: Int64 NULL
74 bit_and(Int8, Int16) :: Int64
75 bit_and(Int8 NULL, Int16 NULL) :: Int64 NULL
76 bit_and(Int8, Int32) :: Int64
77 bit_and(Int8 NULL, Int32 NULL) :: Int64 NULL
78 bit_and(Int8, Int64) :: Int64
79 bit_and(Int8 NULL, Int64 NULL) :: Int64 NULL
80 bit_and(Int16, UInt8) :: Int64
81 bit_and(Int16 NULL, UInt8 NULL) :: Int64 NULL
82 bit_and(Int16, UInt16) :: Int64
83 bit_and(Int16 NULL, UInt16 NULL) :: Int64 NULL
84 bit_and(Int16, UInt32) :: Int64
85 bit_and(Int16 NULL, UInt32 NULL) :: Int64 NULL
86 bit_and(Int16, UInt64) :: Int64
87 bit_and(Int16 NULL, UInt64 NULL) :: Int64 NULL
88 bit_and(Int16, Int8) :: Int64
89 bit_and(Int16 NULL, Int8 NULL) :: Int64 NULL
90 bit_and(Int16, Int16) :: Int64
91 bit_and(Int16 NULL, Int16 NULL) :: Int64 NULL
92 bit_and(Int16, Int32) :: Int64
93 bit_and(Int16 NULL, Int32 NULL) :: Int64 NULL
94 bit_and(Int16, Int64) :: Int64
95 bit_and(Int16 NULL, Int64 NULL) :: Int64 NULL
96 bit_and(Int32, UInt8) :: Int64
97 bit_and(Int32 NULL, UInt8 NULL) :: Int64 NULL
98 bit_and(Int32, UInt16) :: Int64
99 bit_and(Int32 NULL, UInt16 NULL) :: Int64 NULL
100 bit_and(Int32, UInt32) :: Int64
101 bit_and(Int32 NULL, UInt32 NULL) :: Int64 NULL
102 bit_and(Int32, UInt64) :: Int64
103 bit_and(Int32 NULL, UInt64 NULL) :: Int64 NULL
104 bit_and(Int32, Int8) :: Int64
105 bit_and(Int32 NULL, Int8 NULL) :: Int64 NULL
106 bit_and(Int32, Int16) :: Int64
107 bit_and(Int32 NULL, Int16 NULL) :: Int64 NULL
108 bit_and(Int32, Int32) :: Int64
109 bit_and(Int32 NULL, Int32 NULL) :: Int64 NULL
110 bit_and(Int32, Int64) :: Int64
111 bit_and(Int32 NULL, Int64 NULL) :: Int64 NULL
112 bit_and(Int64, UInt8) :: Int64
113 bit_and(Int64 NULL, UInt8 NULL) :: Int64 NULL
114 bit_and(Int64, UInt16) :: Int64
115 bit_and(Int64 NULL, UInt16 NULL) :: Int64 NULL
116 bit_and(Int64, UInt32) :: Int64
117 bit_and(Int64 NULL, UInt32 NULL) :: Int64 NULL
118 bit_and(Int64, UInt64) :: Int64
119 bit_and(Int64 NULL, UInt64 NULL) :: Int64 NULL
120 bit_and(Int64, Int8) :: Int64
121 bit_and(Int64 NULL, Int8 NULL) :: Int64 NULL
122 bit_and(Int64, Int16) :: Int64
123 bit_and(Int64 NULL, Int16 NULL) :: Int64 NULL
124 bit_and(Int64, Int32) :: Int64
125 bit_and(Int64 NULL, Int32 NULL) :: Int64 NULL
126 bit_and(Int64, Int64) :: Int64
127 bit_and(Int64 NULL, Int64 NULL) :: Int64 NULL
0 bit_length(String) :: UInt64
1 bit_length(String NULL) :: UInt64 NULL
0 bit_not(UInt8) :: Int64
1 bit_not(UInt8 NULL) :: Int64 NULL
2 bit_not(UInt16) :: Int64
3 bit_not(UInt16 NULL) :: Int64 NULL
4 bit_not(UInt32) :: Int64
5 bit_not(UInt32 NULL) :: Int64 NULL
6 bit_not(UInt64) :: Int64
7 bit_not(UInt64 NULL) :: Int64 NULL
8 bit_not(Int8) :: Int64
9 bit_not(Int8 NULL) :: Int64 NULL
10 bit_not(Int16) :: Int64
11 bit_not(Int16 NULL) :: Int64 NULL
12 bit_not(Int32) :: Int64
13 bit_not(Int32 NULL) :: Int64 NULL
14 bit_not(Int64) :: Int64
15 bit_not(Int64 NULL) :: Int64 NULL
0 bit_or(UInt8, UInt8) :: Int64
1 bit_or(UInt8 NULL, UInt8 NULL) :: Int64 NULL
2 bit_or(UInt8, UInt16) :: Int64
3 bit_or(UInt8 NULL, UInt16 NULL) :: Int64 NULL
4 bit_or(UInt8, UInt32) :: Int64
5 bit_or(UInt8 NULL, UInt32 NULL) :: Int64 NULL
6 bit_or(UInt8, UInt64) :: Int64
7 bit_or(UInt8 NULL, UInt64 NULL) :: Int64 NULL
8 bit_or(UInt8, Int8) :: Int64
9 bit_or(UInt8 NULL, Int8 NULL) :: Int64 NULL
10 bit_or(UInt8, Int16) :: Int64
11 bit_or(UInt8 NULL, Int16 NULL) :: Int64 NULL
12 bit_or(UInt8, Int32) :: Int64
13 bit_or(UInt8 NULL, Int32 NULL) :: Int64 NULL
14 bit_or(UInt8, Int64) :: Int64
15 bit_or(UInt8 NULL, Int64 NULL) :: Int64 NULL
16 bit_or(UInt16, UInt8) :: Int64
17 bit_or(UInt16 NULL, UInt8 NULL) :: Int64 NULL
18 bit_or(UInt16, UInt16) :: Int64
19 bit_or(UInt16 NULL, UInt16 NULL) :: Int64 NULL
20 bit_or(UInt16, UInt32) :: Int64
21 bit_or(UInt16 NULL, UInt32 NULL) :: Int64 NULL
22 bit_or(UInt16, UInt64) :: Int64
23 bit_or(UInt16 NULL, UInt64 NULL) :: Int64 NULL
24 bit_or(UInt16, Int8) :: Int64
25 bit_or(UInt16 NULL, Int8 NULL) :: Int64 NULL
26 bit_or(UInt16, Int16) :: Int64
27 bit_or(UInt16 NULL, Int16 NULL) :: Int64 NULL
28 bit_or(UInt16, Int32) :: Int64
29 bit_or(UInt16 NULL, Int32 NULL) :: Int64 NULL
30 bit_or(UInt16, Int64) :: Int64
31 bit_or(UInt16 NULL, Int64 NULL) :: Int64 NULL
32 bit_or(UInt32, UInt8) :: Int64
33 bit_or(UInt32 NULL, UInt8 NULL) :: Int64 NULL
34 bit_or(UInt32, UInt16) :: Int64
35 bit_or(UInt32 NULL, UInt16 NULL) :: Int64 NULL
36 bit_or(UInt32, UInt32) :: Int64
37 bit_or(UInt32 NULL, UInt32 NULL) :: Int64 NULL
38 bit_or(UInt32, UInt64) :: Int64
39 bit_or(UInt32 NULL, UInt64 NULL) :: Int64 NULL
40 bit_or(UInt32, Int8) :: Int64
41 bit_or(UInt32 NULL, Int8 NULL) :: Int64 NULL
42 bit_or(UInt32, Int16) :: Int64
43 bit_or(UInt32 NULL, Int16 NULL) :: Int64 NULL
44 bit_or(UInt32, Int32) :: Int64
45 bit_or(UInt32 NULL, Int32 NULL) :: Int64 NULL
46 bit_or(UInt32, Int64) :: Int64
47 bit_or(UInt32 NULL, Int64 NULL) :: Int64 NULL
48 bit_or(UInt64, UInt8) :: Int64
49 bit_or(UInt64 NULL, UInt8 NULL) :: Int64 NULL
50 bit_or(UInt64, UInt16) :: Int64
51 bit_or(UInt64 NULL, UInt16 NULL) :: Int64 NULL
52 bit_or(UInt64, UInt32) :: Int64
53 bit_or(UInt64 NULL, UInt32 NULL) :: Int64 NULL
54 bit_or(UInt64, UInt64) :: Int64
55 bit_or(UInt64 NULL, UInt64 NULL) :: Int64 NULL
56 bit_or(UInt64, Int8) :: Int64
57 bit_or(UInt64 NULL, Int8 NULL) :: Int64 NULL
58 bit_or(UInt64, Int16) :: Int64
59 bit_or(UInt64 NULL, Int16 NULL) :: Int64 NULL
60 bit_or(UInt64, Int32) :: Int64
61 bit_or(UInt64 NULL, Int32 NULL) :: Int64 NULL
62 bit_or(UInt64, Int64) :: Int64
63 bit_or(UInt64 NULL, Int64 NULL) :: Int64 NULL
64 bit_or(Int8, UInt8) :: Int64
65 bit_or(Int8 NULL, UInt8 NULL) :: Int64 NULL
66 bit_or(Int8, UInt16) :: Int64
67 bit_or(Int8 NULL, UInt16 NULL) :: Int64 NULL
68 bit_or(Int8, UInt32) :: Int64
69 bit_or(Int8 NULL, UInt32 NULL) :: Int64 NULL
70 bit_or(Int8, UInt64) :: Int64
71 bit_or(Int8 NULL, UInt64 NULL) :: Int64 NULL
72 bit_or(Int8, Int8) :: Int64
73 bit_or(Int8 NULL, Int8 NULL) :: Int64 NULL
74 bit_or(Int8, Int16) :: Int64
75 bit_or(Int8 NULL, Int16 NULL) :: Int64 NULL
76 bit_or(Int8, Int32) :: Int64
77 bit_or(Int8 NULL, Int32 NULL) :: Int64 NULL
78 bit_or(Int8, Int64) :: Int64
79 bit_or(Int8 NULL, Int64 NULL) :: Int64 NULL
80 bit_or(Int16, UInt8) :: Int64
81 bit_or(Int16 NULL, UInt8 NULL) :: Int64 NULL
82 bit_or(Int16, UInt16) :: Int64
83 bit_or(Int16 NULL, UInt16 NULL) :: Int64 NULL
84 bit_or(Int16, UInt32) :: Int64
85 bit_or(Int16 NULL, UInt32 NULL) :: Int64 NULL
86 bit_or(Int16, UInt64) :: Int64
87 bit_or(Int16 NULL, UInt64 NULL) :: Int64 NULL
88 bit_or(Int16, Int8) :: Int64
89 bit_or(Int16 NULL, Int8 NULL) :: Int64 NULL
90 bit_or(Int16, Int16) :: Int64
91 bit_or(Int16 NULL, Int16 NULL) :: Int64 NULL
92 bit_or(Int16, Int32) :: Int64
93 bit_or(Int16 NULL, Int32 NULL) :: Int64 NULL
94 bit_or(Int16, Int64) :: Int64
95 bit_or(Int16 NULL, Int64 NULL) :: Int64 NULL
96 bit_or(Int32, UInt8) :: Int64
97 bit_or(Int32 NULL, UInt8 NULL) :: Int64 NULL
98 bit_or(Int32, UInt16) :: Int64
99 bit_or(Int32 NULL, UInt16 NULL) :: Int64 NULL
100 bit_or(Int32, UInt32) :: Int64
101 bit_or(Int32 NULL, UInt32 NULL) :: Int64 NULL
102 bit_or(Int32, UInt64) :: Int64
103 bit_or(Int32 NULL, UInt64 NULL) :: Int64 NULL
104 bit_or(Int32, Int8) :: Int64
105 bit_or(Int32 NULL, Int8 NULL) :: Int64 NULL
106 bit_or(Int32, Int16) :: Int64
107 bit_or(Int32 NULL, Int16 NULL) :: Int64 NULL
108 bit_or(Int32, Int32) :: Int64
109 bit_or(Int32 NULL, Int32 NULL) :: Int64 NULL
110 bit_or(Int32, Int64) :: Int64
111 bit_or(Int32 NULL, Int64 NULL) :: Int64 NULL
112 bit_or(Int64, UInt8) :: Int64
113 bit_or(Int64 NULL, UInt8 NULL) :: Int64 NULL
114 bit_or(Int64, UInt16) :: Int64
115 bit_or(Int64 NULL, UInt16 NULL) :: Int64 NULL
116 bit_or(Int64, UInt32) :: Int64
117 bit_or(Int64 NULL, UInt32 NULL) :: Int64 NULL
118 bit_or(Int64, UInt64) :: Int64
119 bit_or(Int64 NULL, UInt64 NULL) :: Int64 NULL
120 bit_or(Int64, Int8) :: Int64
121 bit_or(Int64 NULL, Int8 NULL) :: Int64 NULL
122 bit_or(Int64, Int16) :: Int64
123 bit_or(Int64 NULL, Int16 NULL) :: Int64 NULL
124 bit_or(Int64, Int32) :: Int64
125 bit_or(Int64 NULL, Int32 NULL) :: Int64 NULL
126 bit_or(Int64, Int64) :: Int64
127 bit_or(Int64 NULL, Int64 NULL) :: Int64 NULL
0 bit_shift_left(UInt8, UInt8) :: Int64
1 bit_shift_left(UInt8 NULL, UInt8 NULL) :: Int64 NULL
2 bit_shift_left(UInt8, UInt16) :: Int64
3 bit_shift_left(UInt8 NULL, UInt16 NULL) :: Int64 NULL
4 bit_shift_left(UInt8, UInt32) :: Int64
5 bit_shift_left(UInt8 NULL, UInt32 NULL) :: Int64 NULL
6 bit_shift_left(UInt8, UInt64) :: Int64
7 bit_shift_left(UInt8 NULL, UInt64 NULL) :: Int64 NULL
8 bit_shift_left(UInt16, UInt8) :: Int64
9 bit_shift_left(UInt16 NULL, UInt8 NULL) :: Int64 NULL
10 bit_shift_left(UInt16, UInt16) :: Int64
11 bit_shift_left(UInt16 NULL, UInt16 NULL) :: Int64 NULL
12 bit_shift_left(UInt16, UInt32) :: Int64
13 bit_shift_left(UInt16 NULL, UInt32 NULL) :: Int64 NULL
14 bit_shift_left(UInt16, UInt64) :: Int64
15 bit_shift_left(UInt16 NULL, UInt64 NULL) :: Int64 NULL
16 bit_shift_left(UInt32, UInt8) :: Int64
17 bit_shift_left(UInt32 NULL, UInt8 NULL) :: Int64 NULL
18 bit_shift_left(UInt32, UInt16) :: Int64
19 bit_shift_left(UInt32 NULL, UInt16 NULL) :: Int64 NULL
20 bit_shift_left(UInt32, UInt32) :: Int64
21 bit_shift_left(UInt32 NULL, UInt32 NULL) :: Int64 NULL
22 bit_shift_left(UInt32, UInt64) :: Int64
23 bit_shift_left(UInt32 NULL, UInt64 NULL) :: Int64 NULL
24 bit_shift_left(UInt64, UInt8) :: Int64
25 bit_shift_left(UInt64 NULL, UInt8 NULL) :: Int64 NULL
26 bit_shift_left(UInt64, UInt16) :: Int64
27 bit_shift_left(UInt64 NULL, UInt16 NULL) :: Int64 NULL
28 bit_shift_left(UInt64, UInt32) :: Int64
29 bit_shift_left(UInt64 NULL, UInt32 NULL) :: Int64 NULL
30 bit_shift_left(UInt64, UInt64) :: Int64
31 bit_shift_left(UInt64 NULL, UInt64 NULL) :: Int64 NULL
32 bit_shift_left(Int8, UInt8) :: Int64
33 bit_shift_left(Int8 NULL, UInt8 NULL) :: Int64 NULL
34 bit_shift_left(Int8, UInt16) :: Int64
35 bit_shift_left(Int8 NULL, UInt16 NULL) :: Int64 NULL
36 bit_shift_left(Int8, UInt32) :: Int64
37 bit_shift_left(Int8 NULL, UInt32 NULL) :: Int64 NULL
38 bit_shift_left(Int8, UInt64) :: Int64
39 bit_shift_left(Int8 NULL, UInt64 NULL) :: Int64 NULL
40 bit_shift_left(Int16, UInt8) :: Int64
41 bit_shift_left(Int16 NULL, UInt8 NULL) :: Int64 NULL
42 bit_shift_left(Int16, UInt16) :: Int64
43 bit_shift_left(Int16 NULL, UInt16 NULL) :: Int64 NULL
44 bit_shift_left(Int16, UInt32) :: Int64
45 bit_shift_left(Int16 NULL, UInt32 NULL) :: Int64 NULL
46 bit_shift_left(Int16, UInt64) :: Int64
47 bit_shift_left(Int16 NULL, UInt64 NULL) :: Int64 NULL
48 bit_shift_left(Int32, UInt8) :: Int64
49 bit_shift_left(Int32 NULL, UInt8 NULL) :: Int64 NULL
50 bit_shift_left(Int32, UInt16) :: Int64
51 bit_shift_left(Int32 NULL, UInt16 NULL) :: Int64 NULL
52 bit_shift_left(Int32, UInt32) :: Int64
53 bit_shift_left(Int32 NULL, UInt32 NULL) :: Int64 NULL
54 bit_shift_left(Int32, UInt64) :: Int64
55 bit_shift_left(Int32 NULL, UInt64 NULL) :: Int64 NULL
56 bit_shift_left(Int64, UInt8) :: Int64
57 bit_shift_left(Int64 NULL, UInt8 NULL) :: Int64 NULL
58 bit_shift_left(Int64, UInt16) :: Int64
59 bit_shift_left(Int64 NULL, UInt16 NULL) :: Int64 NULL
60 bit_shift_left(Int64, UInt32) :: Int64
61 bit_shift_left(Int64 NULL, UInt32 NULL) :: Int64 NULL
62 bit_shift_left(Int64, UInt64) :: Int64
63 bit_shift_left(Int64 NULL, UInt64 NULL) :: Int64 NULL
0 bit_shift_right(UInt8, UInt8) :: Int64
1 bit_shift_right(UInt8 NULL, UInt8 NULL) :: Int64 NULL
2 bit_shift_right(UInt8, UInt16) :: Int64
3 bit_shift_right(UInt8 NULL, UInt16 NULL) :: Int64 NULL
4 bit_shift_right(UInt8, UInt32) :: Int64
5 bit_shift_right(UInt8 NULL, UInt32 NULL) :: Int64 NULL
6 bit_shift_right(UInt8, UInt64) :: Int64
7 bit_shift_right(UInt8 NULL, UInt64 NULL) :: Int64 NULL
8 bit_shift_right(UInt16, UInt8) :: Int64
9 bit_shift_right(UInt16 NULL, UInt8 NULL) :: Int64 NULL
10 bit_shift_right(UInt16, UInt16) :: Int64
11 bit_shift_right(UInt16 NULL, UInt16 NULL) :: Int64 NULL
12 bit_shift_right(UInt16, UInt32) :: Int64
13 bit_shift_right(UInt16 NULL, UInt32 NULL) :: Int64 NULL
14 bit_shift_right(UInt16, UInt64) :: Int64
15 bit_shift_right(UInt16 NULL, UInt64 NULL) :: Int64 NULL
16 bit_shift_right(UInt32, UInt8) :: Int64
17 bit_shift_right(UInt32 NULL, UInt8 NULL) :: Int64 NULL
18 bit_shift_right(UInt32, UInt16) :: Int64
19 bit_shift_right(UInt32 NULL, UInt16 NULL) :: Int64 NULL
20 bit_shift_right(UInt32, UInt32) :: Int64
21 bit_shift_right(UInt32 NULL, UInt32 NULL) :: Int64 NULL
22 bit_shift_right(UInt32, UInt64) :: Int64
23 bit_shift_right(UInt32 NULL, UInt64 NULL) :: Int64 NULL
24 bit_shift_right(UInt64, UInt8) :: Int64
25 bit_shift_right(UInt64 NULL, UInt8 NULL) :: Int64 NULL
26 bit_shift_right(UInt64, UInt16) :: Int64
27 bit_shift_right(UInt64 NULL, UInt16 NULL) :: Int64 NULL
28 bit_shift_right(UInt64, UInt32) :: Int64
29 bit_shift_right(UInt64 NULL, UInt32 NULL) :: Int64 NULL
30 bit_shift_right(UInt64, UInt64) :: Int64
31 bit_shift_right(UInt64 NULL, UInt64 NULL) :: Int64 NULL
32 bit_shift_right(Int8, UInt8) :: Int64
33 bit_shift_right(Int8 NULL, UInt8 NULL) :: Int64 NULL
34 bit_shift_right(Int8, UInt16) :: Int64
35 bit_shift_right(Int8 NULL, UInt16 NULL) :: Int64 NULL
36 bit_shift_right(Int8, UInt32) :: Int64
37 bit_shift_right(Int8 NULL, UInt32 NULL) :: Int64 NULL
38 bit_shift_right(Int8, UInt64) :: Int64
39 bit_shift_right(Int8 NULL, UInt64 NULL) :: Int64 NULL
40 bit_shift_right(Int16, UInt8) :: Int64
41 bit_shift_right(Int16 NULL, UInt8 NULL) :: Int64 NULL
42 bit_shift_right(Int16, UInt16) :: Int64
43 bit_shift_right(Int16 NULL, UInt16 NULL) :: Int64 NULL
44 bit_shift_right(Int16, UInt32) :: Int64
45 bit_shift_right(Int16 NULL, UInt32 NULL) :: Int64 NULL
46 bit_shift_right(Int16, UInt64) :: Int64
47 bit_shift_right(Int16 NULL, UInt64 NULL) :: Int64 NULL
48 bit_shift_right(Int32, UInt8) :: Int64
49 bit_shift_right(Int32 NULL, UInt8 NULL) :: Int64 NULL
50 bit_shift_right(Int32, UInt16) :: Int64
51 bit_shift_right(Int32 NULL, UInt16 NULL) :: Int64 NULL
52 bit_shift_right(Int32, UInt32) :: Int64
53 bit_shift_right(Int32 NULL, UInt32 NULL) :: Int64 NULL
54 bit_shift_right(Int32, UInt64) :: Int64
55 bit_shift_right(Int32 NULL, UInt64 NULL) :: Int64 NULL
56 bit_shift_right(Int64, UInt8) :: Int64
57 bit_shift_right(Int64 NULL, UInt8 NULL) :: Int64 NULL
58 bit_shift_right(Int64, UInt16) :: Int64
59 bit_shift_right(Int64 NULL, UInt16 NULL) :: Int64 NULL
60 bit_shift_right(Int64, UInt32) :: Int64
61 bit_shift_right(Int64 NULL, UInt32 NULL) :: Int64 NULL
62 bit_shift_right(Int64, UInt64) :: Int64
63 bit_shift_right(Int64 NULL, UInt64 NULL) :: Int64 NULL
0 bit_xor(UInt8, UInt8) :: Int64
1 bit_xor(UInt8 NULL, UInt8 NULL) :: Int64 NULL
2 bit_xor(UInt8, UInt16) :: Int64
3 bit_xor(UInt8 NULL, UInt16 NULL) :: Int64 NULL
4 bit_xor(UInt8, UInt32) :: Int64
5 bit_xor(UInt8 NULL, UInt32 NULL) :: Int64 NULL
6 bit_xor(UInt8, UInt64) :: Int64
7 bit_xor(UInt8 NULL, UInt64 NULL) :: Int64 NULL
8 bit_xor(UInt8, Int8) :: Int64
9 bit_xor(UInt8 NULL, Int8 NULL) :: Int64 NULL
10 bit_xor(UInt8, Int16) :: Int64
11 bit_xor(UInt8 NULL, Int16 NULL) :: Int64 NULL
12 bit_xor(UInt8, Int32) :: Int64
13 bit_xor(UInt8 NULL, Int32 NULL) :: Int64 NULL
14 bit_xor(UInt8, Int64) :: Int64
15 bit_xor(UInt8 NULL, Int64 NULL) :: Int64 NULL
16 bit_xor(UInt16, UInt8) :: Int64
17 bit_xor(UInt16 NULL, UInt8 NULL) :: Int64 NULL
18 bit_xor(UInt16, UInt16) :: Int64
19 bit_xor(UInt16 NULL, UInt16 NULL) :: Int64 NULL
20 bit_xor(UInt16, UInt32) :: Int64
21 bit_xor(UInt16 NULL, UInt32 NULL) :: Int64 NULL
22 bit_xor(UInt16, UInt64) :: Int64
23 bit_xor(UInt16 NULL, UInt64 NULL) :: Int64 NULL
24 bit_xor(UInt16, Int8) :: Int64
25 bit_xor(UInt16 NULL, Int8 NULL) :: Int64 NULL
26 bit_xor(UInt16, Int16) :: Int64
27 bit_xor(UInt16 NULL, Int16 NULL) :: Int64 NULL
28 bit_xor(UInt16, Int32) :: Int64
29 bit_xor(UInt16 NULL, Int32 NULL) :: Int64 NULL
30 bit_xor(UInt16, Int64) :: Int64
31 bit_xor(UInt16 NULL, Int64 NULL) :: Int64 NULL
32 bit_xor(UInt32, UInt8) :: Int64
33 bit_xor(UInt32 NULL, UInt8 NULL) :: Int64 NULL
34 bit_xor(UInt32, UInt16) :: Int64
35 bit_xor(UInt32 NULL, UInt16 NULL) :: Int64 NULL
36 bit_xor(UInt32, UInt32) :: Int64
37 bit_xor(UInt32 NULL, UInt32 NULL) :: Int64 NULL
38 bit_xor(UInt32, UInt64) :: Int64
39 bit_xor(UInt32 NULL, UInt64 NULL) :: Int64 NULL
40 bit_xor(UInt32, Int8) :: Int64
41 bit_xor(UInt32 NULL, Int8 NULL) :: Int64 NULL
42 bit_xor(UInt32, Int16) :: Int64
43 bit_xor(UInt32 NULL, Int16 NULL) :: Int64 NULL
44 bit_xor(UInt32, Int32) :: Int64
45 bit_xor(UInt32 NULL, Int32 NULL) :: Int64 NULL
46 bit_xor(UInt32, Int64) :: Int64
47 bit_xor(UInt32 NULL, Int64 NULL) :: Int64 NULL
48 bit_xor(UInt64, UInt8) :: Int64
49 bit_xor(UInt64 NULL, UInt8 NULL) :: Int64 NULL
50 bit_xor(UInt64, UInt16) :: Int64
51 bit_xor(UInt64 NULL, UInt16 NULL) :: Int64 NULL
52 bit_xor(UInt64, UInt32) :: Int64
53 bit_xor(UInt64 NULL, UInt32 NULL) :: Int64 NULL
54 bit_xor(UInt64, UInt64) :: Int64
55 bit_xor(UInt64 NULL, UInt64 NULL) :: Int64 NULL
56 bit_xor(UInt64, Int8) :: Int64
57 bit_xor(UInt64 NULL, Int8 NULL) :: Int64 NULL
58 bit_xor(UInt64, Int16) :: Int64
59 bit_xor(UInt64 NULL, Int16 NULL) :: Int64 NULL
60 bit_xor(UInt64, Int32) :: Int64
61 bit_xor(UInt64 NULL, Int32 NULL) :: Int64 NULL
62 bit_xor(UInt64, Int64) :: Int64
63 bit_xor(UInt64 NULL, Int64 NULL) :: Int64 NULL
64 bit_xor(Int8, UInt8) :: Int64
65 bit_xor(Int8 NULL, UInt8 NULL) :: Int64 NULL
66 bit_xor(Int8, UInt16) :: Int64
67 bit_xor(Int8 NULL, UInt16 NULL) :: Int64 NULL
68 bit_xor(Int8, UInt32) :: Int64
69 bit_xor(Int8 NULL, UInt32 NULL) :: Int64 NULL
70 bit_xor(Int8, UInt64) :: Int64
71 bit_xor(Int8 NULL, UInt64 NULL) :: Int64 NULL
72 bit_xor(Int8, Int8) :: Int64
73 bit_xor(Int8 NULL, Int8 NULL) :: Int64 NULL
74 bit_xor(Int8, Int16) :: Int64
75 bit_xor(Int8 NULL, Int16 NULL) :: Int64 NULL
76 bit_xor(Int8, Int32) :: Int64
77 bit_xor(Int8 NULL, Int32 NULL) :: Int64 NULL
78 bit_xor(Int8, Int64) :: Int64
79 bit_xor(Int8 NULL, Int64 NULL) :: Int64 NULL
80 bit_xor(Int16, UInt8) :: Int64
81 bit_xor(Int16 NULL, UInt8 NULL) :: Int64 NULL
82 bit_xor(Int16, UInt16) :: Int64
83 bit_xor(Int16 NULL, UInt16 NULL) :: Int64 NULL
84 bit_xor(Int16, UInt32) :: Int64
85 bit_xor(Int16 NULL, UInt32 NULL) :: Int64 NULL
86 bit_xor(Int16, UInt64) :: Int64
87 bit_xor(Int16 NULL, UInt64 NULL) :: Int64 NULL
88 bit_xor(Int16, Int8) :: Int64
89 bit_xor(Int16 NULL, Int8 NULL) :: Int64 NULL
90 bit_xor(Int16, Int16) :: Int64
91 bit_xor(Int16 NULL, Int16 NULL) :: Int64 NULL
92 bit_xor(Int16, Int32) :: Int64
93 bit_xor(Int16 NULL, Int32 NULL) :: Int64 NULL
94 bit_xor(Int16, Int64) :: Int64
95 bit_xor(Int16 NULL, Int64 NULL) :: Int64 NULL
96 bit_xor(Int32, UInt8) :: Int64
97 bit_xor(Int32 NULL, UInt8 NULL) :: Int64 NULL
98 bit_xor(Int32, UInt16) :: Int64
99 bit_xor(Int32 NULL, UInt16 NULL) :: Int64 NULL
100 bit_xor(Int32, UInt32) :: Int64
101 bit_xor(Int32 NULL, UInt32 NULL) :: Int64 NULL
102 bit_xor(Int32, UInt64) :: Int64
103 bit_xor(Int32 NULL, UInt64 NULL) :: Int64 NULL
104 bit_xor(Int32, Int8) :: Int64
105 bit_xor(Int32 NULL, Int8 NULL) :: Int64 NULL
106 bit_xor(Int32, Int16) :: Int64
107 bit_xor(Int32 NULL, Int16 NULL) :: Int64 NULL
108 bit_xor(Int32, Int32) :: Int64
109 bit_xor(Int32 NULL, Int32 NULL) :: Int64 NULL
110 bit_xor(Int32, Int64) :: Int64
111 bit_xor(Int32 NULL, Int64 NULL) :: Int64 NULL
112 bit_xor(Int64, UInt8) :: Int64
113 bit_xor(Int64 NULL, UInt8 NULL) :: Int64 NULL
114 bit_xor(Int64, UInt16) :: Int64
115 bit_xor(Int64 NULL, UInt16 NULL) :: Int64 NULL
116 bit_xor(Int64, UInt32) :: Int64
117 bit_xor(Int64 NULL, UInt32 NULL) :: Int64 NULL
118 bit_xor(Int64, UInt64) :: Int64
119 bit_xor(Int64 NULL, UInt64 NULL) :: Int64 NULL
120 bit_xor(Int64, Int8) :: Int64
121 bit_xor(Int64 NULL, Int8 NULL) :: Int64 NULL
122 bit_xor(Int64, Int16) :: Int64
123 bit_xor(Int64 NULL, Int16 NULL) :: Int64 NULL
124 bit_xor(Int64, Int32) :: Int64
125 bit_xor(Int64 NULL, Int32 NULL) :: Int64 NULL
126 bit_xor(Int64, Int64) :: Int64
127 bit_xor(Int64 NULL, Int64 NULL) :: Int64 NULL
0 bitmap_and(Bitmap, Bitmap) :: Bitmap
1 bitmap_and(Bitmap NULL, Bitmap NULL) :: Bitmap NULL
0 bitmap_contains(Bitmap, UInt64) :: Boolean
1 bitmap_contains(Bitmap NULL, UInt64 NULL) :: Boolean NULL
0 bitmap_count(Bitmap) :: UInt64
1 bitmap_count(Bitmap NULL) :: UInt64 NULL
0 bitmap_has_all(Bitmap, Bitmap) :: Boolean
1 bitmap_has_all(Bitmap NULL, Bitmap NULL) :: Boolean NULL
0 bitmap_has_any(Bitmap, Bitmap) :: Boolean
1 bitmap_has_any(Bitmap NULL, Bitmap NULL) :: Boolean NULL
0 bitmap_max(Bitmap) :: UInt64
1 bitmap_max(Bitmap NULL) :: UInt64 NULL
0 bitmap_min(Bitmap) :: UInt64
1 bitmap_min(Bitmap NULL) :: UInt64 NULL
0 bitmap_not(Bitmap, Bitmap) :: Bitmap
1 bitmap_not(Bitmap NULL, Bitmap NULL) :: Bitmap NULL
0 bitmap_or(Bitmap, Bitmap) :: Bitmap
1 bitmap_or(Bitmap NULL, Bitmap NULL) :: Bitmap NULL
0 bitmap_subset_in_range(Bitmap, UInt64, UInt64) :: Bitmap
1 bitmap_subset_in_range(Bitmap NULL, UInt64 NULL, UInt64 NULL) :: Bitmap NULL
0 bitmap_subset_limit(Bitmap, UInt64, UInt64) :: Bitmap
1 bitmap_subset_limit(Bitmap NULL, UInt64 NULL, UInt64 NULL) :: Bitmap NULL
0 bitmap_xor(Bitmap, Bitmap) :: Bitmap
1 bitmap_xor(Bitmap NULL, Bitmap NULL) :: Bitmap NULL
0 blake3(String) :: String
1 blake3(String NULL) :: String NULL
0 build_bitmap(Array(UInt8 NULL)) :: Bitmap
1 build_bitmap(Array(UInt8 NULL) NULL) :: Bitmap NULL
2 build_bitmap(Array(UInt16 NULL)) :: Bitmap
3 build_bitmap(Array(UInt16 NULL) NULL) :: Bitmap NULL
4 build_bitmap(Array(UInt32 NULL)) :: Bitmap
5 build_bitmap(Array(UInt32 NULL) NULL) :: Bitmap NULL
6 build_bitmap(Array(UInt64 NULL)) :: Bitmap
7 build_bitmap(Array(UInt64 NULL) NULL) :: Bitmap NULL
8 build_bitmap(Array(Int8 NULL)) :: Bitmap
9 build_bitmap(Array(Int8 NULL) NULL) :: Bitmap NULL
10 build_bitmap(Array(Int16 NULL)) :: Bitmap
11 build_bitmap(Array(Int16 NULL) NULL) :: Bitmap NULL
12 build_bitmap(Array(Int32 NULL)) :: Bitmap
13 build_bitmap(Array(Int32 NULL) NULL) :: Bitmap NULL
14 build_bitmap(Array(Int64 NULL)) :: Bitmap
15 build_bitmap(Array(Int64 NULL) NULL) :: Bitmap NULL
0 cbrt(UInt8) :: Float64
1 cbrt(UInt8 NULL) :: Float64 NULL
2 cbrt(UInt16) :: Float64
3 cbrt(UInt16 NULL) :: Float64 NULL
4 cbrt(UInt32) :: Float64
5 cbrt(UInt32 NULL) :: Float64 NULL
6 cbrt(UInt64) :: Float64
7 cbrt(UInt64 NULL) :: Float64 NULL
8 cbrt(Int8) :: Float64
9 cbrt(Int8 NULL) :: Float64 NULL
10 cbrt(Int16) :: Float64
11 cbrt(Int16 NULL) :: Float64 NULL
12 cbrt(Int32) :: Float64
13 cbrt(Int32 NULL) :: Float64 NULL
14 cbrt(Int64) :: Float64
15 cbrt(Int64 NULL) :: Float64 NULL
16 cbrt(Float32) :: Float64
17 cbrt(Float32 NULL) :: Float64 NULL
18 cbrt(Float64) :: Float64
19 cbrt(Float64 NULL) :: Float64 NULL
0 ceil FACTORY
1 ceil(UInt8) :: UInt8
2 ceil(UInt8 NULL) :: UInt8 NULL
3 ceil(UInt16) :: UInt16
4 ceil(UInt16 NULL) :: UInt16 NULL
5 ceil(UInt32) :: UInt32
6 ceil(UInt32 NULL) :: UInt32 NULL
7 ceil(UInt64) :: UInt64
8 ceil(UInt64 NULL) :: UInt64 NULL
9 ceil(Int8) :: Int8
10 ceil(Int8 NULL) :: Int8 NULL
11 ceil(Int16) :: Int16
12 ceil(Int16 NULL) :: Int16 NULL
13 ceil(Int32) :: Int32
14 ceil(Int32 NULL) :: Int32 NULL
15 ceil(Int64) :: Int64
16 ceil(Int64 NULL) :: Int64 NULL
17 ceil(Float32) :: Float64
18 ceil(Float32 NULL) :: Float64 NULL
19 ceil(Float64) :: Float64
20 ceil(Float64 NULL) :: Float64 NULL
0 char FACTORY
0 check_json(Variant) :: String NULL
1 check_json(Variant NULL) :: String NULL
2 check_json(String) :: String NULL
3 check_json(String NULL) :: String NULL
0 city64withseed(Variant, UInt8) :: UInt64
1 city64withseed(Variant NULL, UInt8 NULL) :: UInt64 NULL
2 city64withseed(Variant, UInt16) :: UInt64
3 city64withseed(Variant NULL, UInt16 NULL) :: UInt64 NULL
4 city64withseed(Variant, UInt32) :: UInt64
5 city64withseed(Variant NULL, UInt32 NULL) :: UInt64 NULL
6 city64withseed(Variant, UInt64) :: UInt64
7 city64withseed(Variant NULL, UInt64 NULL) :: UInt64 NULL
8 city64withseed(Variant, Int8) :: UInt64
9 city64withseed(Variant NULL, Int8 NULL) :: UInt64 NULL
10 city64withseed(Variant, Int16) :: UInt64
11 city64withseed(Variant NULL, Int16 NULL) :: UInt64 NULL
12 city64withseed(Variant, Int32) :: UInt64
13 city64withseed(Variant NULL, Int32 NULL) :: UInt64 NULL
14 city64withseed(Variant, Int64) :: UInt64
15 city64withseed(Variant NULL, Int64 NULL) :: UInt64 NULL
16 city64withseed(Variant, Float32) :: UInt64
17 city64withseed(Variant NULL, Float32 NULL) :: UInt64 NULL
18 city64withseed(Variant, Float64) :: UInt64
19 city64withseed(Variant NULL, Float64 NULL) :: UInt64 NULL
20 city64withseed(String, UInt8) :: UInt64
21 city64withseed(String NULL, UInt8 NULL) :: UInt64 NULL
22 city64withseed(String, UInt16) :: UInt64
23 city64withseed(String NULL, UInt16 NULL) :: UInt64 NULL
24 city64withseed(String, UInt32) :: UInt64
25 city64withseed(String NULL, UInt32 NULL) :: UInt64 NULL
26 city64withseed(String, UInt64) :: UInt64
27 city64withseed(String NULL, UInt64 NULL) :: UInt64 NULL
28 city64withseed(String, Int8) :: UInt64
29 city64withseed(String NULL, Int8 NULL) :: UInt64 NULL
30 city64withseed(String, Int16) :: UInt64
31 city64withseed(String NULL, Int16 NULL) :: UInt64 NULL
32 city64withseed(String, Int32) :: UInt64
33 city64withseed(String NULL, Int32 NULL) :: UInt64 NULL
34 city64withseed(String, Int64) :: UInt64
35 city64withseed(String NULL, Int64 NULL) :: UInt64 NULL
36 city64withseed(String, Float32) :: UInt64
37 city64withseed(String NULL, Float32 NULL) :: UInt64 NULL
38 city64withseed(String, Float64) :: UInt64
39 city64withseed(String NULL, Float64 NULL) :: UInt64 NULL
40 city64withseed(Date, UInt8) :: UInt64
41 city64withseed(Date NULL, UInt8 NULL) :: UInt64 NULL
42 city64withseed(Date, UInt16) :: UInt64
43 city64withseed(Date NULL, UInt16 NULL) :: UInt64 NULL
44 city64withseed(Date, UInt32) :: UInt64
45 city64withseed(Date NULL, UInt32 NULL) :: UInt64 NULL
46 city64withseed(Date, UInt64) :: UInt64
47 city64withseed(Date NULL, UInt64 NULL) :: UInt64 NULL
48 city64withseed(Date, Int8) :: UInt64
49 city64withseed(Date NULL, Int8 NULL) :: UInt64 NULL
50 city64withseed(Date, Int16) :: UInt64
51 city64withseed(Date NULL, Int16 NULL) :: UInt64 NULL
52 city64withseed(Date, Int32) :: UInt64
53 city64withseed(Date NULL, Int32 NULL) :: UInt64 NULL
54 city64withseed(Date, Int64) :: UInt64
55 city64withseed(Date NULL, Int64 NULL) :: UInt64 NULL
56 city64withseed(Date, Float32) :: UInt64
57 city64withseed(Date NULL, Float32 NULL) :: UInt64 NULL
58 city64withseed(Date, Float64) :: UInt64
59 city64withseed(Date NULL, Float64 NULL) :: UInt64 NULL
60 city64withseed(Timestamp, UInt8) :: UInt64
61 city64withseed(Timestamp NULL, UInt8 NULL) :: UInt64 NULL
62 city64withseed(Timestamp, UInt16) :: UInt64
63 city64withseed(Timestamp NULL, UInt16 NULL) :: UInt64 NULL
64 city64withseed(Timestamp, UInt32) :: UInt64
65 city64withseed(Timestamp NULL, UInt32 NULL) :: UInt64 NULL
66 city64withseed(Timestamp, UInt64) :: UInt64
67 city64withseed(Timestamp NULL, UInt64 NULL) :: UInt64 NULL
68 city64withseed(Timestamp, Int8) :: UInt64
69 city64withseed(Timestamp NULL, Int8 NULL) :: UInt64 NULL
70 city64withseed(Timestamp, Int16) :: UInt64
71 city64withseed(Timestamp NULL, Int16 NULL) :: UInt64 NULL
72 city64withseed(Timestamp, Int32) :: UInt64
73 city64withseed(Timestamp NULL, Int32 NULL) :: UInt64 NULL
74 city64withseed(Timestamp, Int64) :: UInt64
75 city64withseed(Timestamp NULL, Int64 NULL) :: UInt64 NULL
76 city64withseed(Timestamp, Float32) :: UInt64
77 city64withseed(Timestamp NULL, Float32 NULL) :: UInt64 NULL
78 city64withseed(Timestamp, Float64) :: UInt64
79 city64withseed(Timestamp NULL, Float64 NULL) :: UInt64 NULL
80 city64withseed(Boolean, UInt8) :: UInt64
81 city64withseed(Boolean NULL, UInt8 NULL) :: UInt64 NULL
82 city64withseed(Boolean, UInt16) :: UInt64
83 city64withseed(Boolean NULL, UInt16 NULL) :: UInt64 NULL
84 city64withseed(Boolean, UInt32) :: UInt64
85 city64withseed(Boolean NULL, UInt32 NULL) :: UInt64 NULL
86 city64withseed(Boolean, UInt64) :: UInt64
87 city64withseed(Boolean NULL, UInt64 NULL) :: UInt64 NULL
88 city64withseed(Boolean, Int8) :: UInt64
89 city64withseed(Boolean NULL, Int8 NULL) :: UInt64 NULL
90 city64withseed(Boolean, Int16) :: UInt64
91 city64withseed(Boolean NULL, Int16 NULL) :: UInt64 NULL
92 city64withseed(Boolean, Int32) :: UInt64
93 city64withseed(Boolean NULL, Int32 NULL) :: UInt64 NULL
94 city64withseed(Boolean, Int64) :: UInt64
95 city64withseed(Boolean NULL, Int64 NULL) :: UInt64 NULL
96 city64withseed(Boolean, Float32) :: UInt64
97 city64withseed(Boolean NULL, Float32 NULL) :: UInt64 NULL
98 city64withseed(Boolean, Float64) :: UInt64
99 city64withseed(Boolean NULL, Float64 NULL) :: UInt64 NULL
100 city64withseed(Bitmap, UInt8) :: UInt64
101 city64withseed(Bitmap NULL, UInt8 NULL) :: UInt64 NULL
102 city64withseed(Bitmap, UInt16) :: UInt64
103 city64withseed(Bitmap NULL, UInt16 NULL) :: UInt64 NULL
104 city64withseed(Bitmap, UInt32) :: UInt64
105 city64withseed(Bitmap NULL, UInt32 NULL) :: UInt64 NULL
106 city64withseed(Bitmap, UInt64) :: UInt64
107 city64withseed(Bitmap NULL, UInt64 NULL) :: UInt64 NULL
108 city64withseed(Bitmap, Int8) :: UInt64
109 city64withseed(Bitmap NULL, Int8 NULL) :: UInt64 NULL
110 city64withseed(Bitmap, Int16) :: UInt64
111 city64withseed(Bitmap NULL, Int16 NULL) :: UInt64 NULL
112 city64withseed(Bitmap, Int32) :: UInt64
113 city64withseed(Bitmap NULL, Int32 NULL) :: UInt64 NULL
114 city64withseed(Bitmap, Int64) :: UInt64
115 city64withseed(Bitmap NULL, Int64 NULL) :: UInt64 NULL
116 city64withseed(Bitmap, Float32) :: UInt64
117 city64withseed(Bitmap NULL, Float32 NULL) :: UInt64 NULL
118 city64withseed(Bitmap, Float64) :: UInt64
119 city64withseed(Bitmap NULL, Float64 NULL) :: UInt64 NULL
120 city64withseed(UInt8, UInt8) :: UInt64
121 city64withseed(UInt8 NULL, UInt8 NULL) :: UInt64 NULL
122 city64withseed(UInt8, UInt16) :: UInt64
123 city64withseed(UInt8 NULL, UInt16 NULL) :: UInt64 NULL
124 city64withseed(UInt8, UInt32) :: UInt64
125 city64withseed(UInt8 NULL, UInt32 NULL) :: UInt64 NULL
126 city64withseed(UInt8, UInt64) :: UInt64
127 city64withseed(UInt8 NULL, UInt64 NULL) :: UInt64 NULL
128 city64withseed(UInt8, Int8) :: UInt64
129 city64withseed(UInt8 NULL, Int8 NULL) :: UInt64 NULL
130 city64withseed(UInt8, Int16) :: UInt64
131 city64withseed(UInt8 NULL, Int16 NULL) :: UInt64 NULL
132 city64withseed(UInt8, Int32) :: UInt64
133 city64withseed(UInt8 NULL, Int32 NULL) :: UInt64 NULL
134 city64withseed(UInt8, Int64) :: UInt64
135 city64withseed(UInt8 NULL, Int64 NULL) :: UInt64 NULL
136 city64withseed(UInt8, Float32) :: UInt64
137 city64withseed(UInt8 NULL, Float32 NULL) :: UInt64 NULL
138 city64withseed(UInt8, Float64) :: UInt64
139 city64withseed(UInt8 NULL, Float64 NULL) :: UInt64 NULL
140 city64withseed(Int8, UInt8) :: UInt64
141 city64withseed(Int8 NULL, UInt8 NULL) :: UInt64 NULL
142 city64withseed(Int8, UInt16) :: UInt64
143 city64withseed(Int8 NULL, UInt16 NULL) :: UInt64 NULL
144 city64withseed(Int8, UInt32) :: UInt64
145 city64withseed(Int8 NULL, UInt32 NULL) :: UInt64 NULL
146 city64withseed(Int8, UInt64) :: UInt64
147 city64withseed(Int8 NULL, UInt64 NULL) :: UInt64 NULL
148 city64withseed(Int8, Int8) :: UInt64
149 city64withseed(Int8 NULL, Int8 NULL) :: UInt64 NULL
150 city64withseed(Int8, Int16) :: UInt64
151 city64withseed(Int8 NULL, Int16 NULL) :: UInt64 NULL
152 city64withseed(Int8, Int32) :: UInt64
153 city64withseed(Int8 NULL, Int32 NULL) :: UInt64 NULL
154 city64withseed(Int8, Int64) :: UInt64
155 city64withseed(Int8 NULL, Int64 NULL) :: UInt64 NULL
156 city64withseed(Int8, Float32) :: UInt64
157 city64withseed(Int8 NULL, Float32 NULL) :: UInt64 NULL
158 city64withseed(Int8, Float64) :: UInt64
159 city64withseed(Int8 NULL, Float64 NULL) :: UInt64 NULL
160 city64withseed(UInt16, UInt8) :: UInt64
161 city64withseed(UInt16 NULL, UInt8 NULL) :: UInt64 NULL
162 city64withseed(UInt16, UInt16) :: UInt64
163 city64withseed(UInt16 NULL, UInt16 NULL) :: UInt64 NULL
164 city64withseed(UInt16, UInt32) :: UInt64
165 city64withseed(UInt16 NULL, UInt32 NULL) :: UInt64 NULL
166 city64withseed(UInt16, UInt64) :: UInt64
167 city64withseed(UInt16 NULL, UInt64 NULL) :: UInt64 NULL
168 city64withseed(UInt16, Int8) :: UInt64
169 city64withseed(UInt16 NULL, Int8 NULL) :: UInt64 NULL
170 city64withseed(UInt16, Int16) :: UInt64
171 city64withseed(UInt16 NULL, Int16 NULL) :: UInt64 NULL
172 city64withseed(UInt16, Int32) :: UInt64
173 city64withseed(UInt16 NULL, Int32 NULL) :: UInt64 NULL
174 city64withseed(UInt16, Int64) :: UInt64
175 city64withseed(UInt16 NULL, Int64 NULL) :: UInt64 NULL
176 city64withseed(UInt16, Float32) :: UInt64
177 city64withseed(UInt16 NULL, Float32 NULL) :: UInt64 NULL
178 city64withseed(UInt16, Float64) :: UInt64
179 city64withseed(UInt16 NULL, Float64 NULL) :: UInt64 NULL
180 city64withseed(Int16, UInt8) :: UInt64
181 city64withseed(Int16 NULL, UInt8 NULL) :: UInt64 NULL
182 city64withseed(Int16, UInt16) :: UInt64
183 city64withseed(Int16 NULL, UInt16 NULL) :: UInt64 NULL
184 city64withseed(Int16, UInt32) :: UInt64
185 city64withseed(Int16 NULL, UInt32 NULL) :: UInt64 NULL
186 city64withseed(Int16, UInt64) :: UInt64
187 city64withseed(Int16 NULL, UInt64 NULL) :: UInt64 NULL
188 city64withseed(Int16, Int8) :: UInt64
189 city64withseed(Int16 NULL, Int8 NULL) :: UInt64 NULL
190 city64withseed(Int16, Int16) :: UInt64
191 city64withseed(Int16 NULL, Int16 NULL) :: UInt64 NULL
192 city64withseed(Int16, Int32) :: UInt64
193 city64withseed(Int16 NULL, Int32 NULL) :: UInt64 NULL
194 city64withseed(Int16, Int64) :: UInt64
195 city64withseed(Int16 NULL, Int64 NULL) :: UInt64 NULL
196 city64withseed(Int16, Float32) :: UInt64
197 city64withseed(Int16 NULL, Float32 NULL) :: UInt64 NULL
198 city64withseed(Int16, Float64) :: UInt64
199 city64withseed(Int16 NULL, Float64 NULL) :: UInt64 NULL
200 city64withseed(UInt32, UInt8) :: UInt64
201 city64withseed(UInt32 NULL, UInt8 NULL) :: UInt64 NULL
202 city64withseed(UInt32, UInt16) :: UInt64
203 city64withseed(UInt32 NULL, UInt16 NULL) :: UInt64 NULL
204 city64withseed(UInt32, UInt32) :: UInt64
205 city64withseed(UInt32 NULL, UInt32 NULL) :: UInt64 NULL
206 city64withseed(UInt32, UInt64) :: UInt64
207 city64withseed(UInt32 NULL, UInt64 NULL) :: UInt64 NULL
208 city64withseed(UInt32, Int8) :: UInt64
209 city64withseed(UInt32 NULL, Int8 NULL) :: UInt64 NULL
210 city64withseed(UInt32, Int16) :: UInt64
211 city64withseed(UInt32 NULL, Int16 NULL) :: UInt64 NULL
212 city64withseed(UInt32, Int32) :: UInt64
213 city64withseed(UInt32 NULL, Int32 NULL) :: UInt64 NULL
214 city64withseed(UInt32, Int64) :: UInt64
215 city64withseed(UInt32 NULL, Int64 NULL) :: UInt64 NULL
216 city64withseed(UInt32, Float32) :: UInt64
217 city64withseed(UInt32 NULL, Float32 NULL) :: UInt64 NULL
218 city64withseed(UInt32, Float64) :: UInt64
219 city64withseed(UInt32 NULL, Float64 NULL) :: UInt64 NULL
220 city64withseed(Int32, UInt8) :: UInt64
221 city64withseed(Int32 NULL, UInt8 NULL) :: UInt64 NULL
222 city64withseed(Int32, UInt16) :: UInt64
223 city64withseed(Int32 NULL, UInt16 NULL) :: UInt64 NULL
224 city64withseed(Int32, UInt32) :: UInt64
225 city64withseed(Int32 NULL, UInt32 NULL) :: UInt64 NULL
226 city64withseed(Int32, UInt64) :: UInt64
227 city64withseed(Int32 NULL, UInt64 NULL) :: UInt64 NULL
228 city64withseed(Int32, Int8) :: UInt64
229 city64withseed(Int32 NULL, Int8 NULL) :: UInt64 NULL
230 city64withseed(Int32, Int16) :: UInt64
231 city64withseed(Int32 NULL, Int16 NULL) :: UInt64 NULL
232 city64withseed(Int32, Int32) :: UInt64
233 city64withseed(Int32 NULL, Int32 NULL) :: UInt64 NULL
234 city64withseed(Int32, Int64) :: UInt64
235 city64withseed(Int32 NULL, Int64 NULL) :: UInt64 NULL
236 city64withseed(Int32, Float32) :: UInt64
237 city64withseed(Int32 NULL, Float32 NULL) :: UInt64 NULL
238 city64withseed(Int32, Float64) :: UInt64
239 city64withseed(Int32 NULL, Float64 NULL) :: UInt64 NULL
240 city64withseed(UInt64, UInt8) :: UInt64
241 city64withseed(UInt64 NULL, UInt8 NULL) :: UInt64 NULL
242 city64withseed(UInt64, UInt16) :: UInt64
243 city64withseed(UInt64 NULL, UInt16 NULL) :: UInt64 NULL
244 city64withseed(UInt64, UInt32) :: UInt64
245 city64withseed(UInt64 NULL, UInt32 NULL) :: UInt64 NULL
246 city64withseed(UInt64, UInt64) :: UInt64
247 city64withseed(UInt64 NULL, UInt64 NULL) :: UInt64 NULL
248 city64withseed(UInt64, Int8) :: UInt64
249 city64withseed(UInt64 NULL, Int8 NULL) :: UInt64 NULL
250 city64withseed(UInt64, Int16) :: UInt64
251 city64withseed(UInt64 NULL, Int16 NULL) :: UInt64 NULL
252 city64withseed(UInt64, Int32) :: UInt64
253 city64withseed(UInt64 NULL, Int32 NULL) :: UInt64 NULL
254 city64withseed(UInt64, Int64) :: UInt64
255 city64withseed(UInt64 NULL, Int64 NULL) :: UInt64 NULL
256 city64withseed(UInt64, Float32) :: UInt64
257 city64withseed(UInt64 NULL, Float32 NULL) :: UInt64 NULL
258 city64withseed(UInt64, Float64) :: UInt64
259 city64withseed(UInt64 NULL, Float64 NULL) :: UInt64 NULL
260 city64withseed(Int64, UInt8) :: UInt64
261 city64withseed(Int64 NULL, UInt8 NULL) :: UInt64 NULL
262 city64withseed(Int64, UInt16) :: UInt64
263 city64withseed(Int64 NULL, UInt16 NULL) :: UInt64 NULL
264 city64withseed(Int64, UInt32) :: UInt64
265 city64withseed(Int64 NULL, UInt32 NULL) :: UInt64 NULL
266 city64withseed(Int64, UInt64) :: UInt64
267 city64withseed(Int64 NULL, UInt64 NULL) :: UInt64 NULL
268 city64withseed(Int64, Int8) :: UInt64
269 city64withseed(Int64 NULL, Int8 NULL) :: UInt64 NULL
270 city64withseed(Int64, Int16) :: UInt64
271 city64withseed(Int64 NULL, Int16 NULL) :: UInt64 NULL
272 city64withseed(Int64, Int32) :: UInt64
273 city64withseed(Int64 NULL, Int32 NULL) :: UInt64 NULL
274 city64withseed(Int64, Int64) :: UInt64
275 city64withseed(Int64 NULL, Int64 NULL) :: UInt64 NULL
276 city64withseed(Int64, Float32) :: UInt64
277 city64withseed(Int64 NULL, Float32 NULL) :: UInt64 NULL
278 city64withseed(Int64, Float64) :: UInt64
279 city64withseed(Int64 NULL, Float64 NULL) :: UInt64 NULL
280 city64withseed FACTORY
281 city64withseed(Float32, UInt8) :: UInt64
282 city64withseed(Float32 NULL, UInt8 NULL) :: UInt64 NULL
283 city64withseed(Float32, UInt16) :: UInt64
284 city64withseed(Float32 NULL, UInt16 NULL) :: UInt64 NULL
285 city64withseed(Float32, UInt32) :: UInt64
286 city64withseed(Float32 NULL, UInt32 NULL) :: UInt64 NULL
287 city64withseed(Float32, UInt64) :: UInt64
288 city64withseed(Float32 NULL, UInt64 NULL) :: UInt64 NULL
289 city64withseed(Float32, Int8) :: UInt64
290 city64withseed(Float32 NULL, Int8 NULL) :: UInt64 NULL
291 city64withseed(Float32, Int16) :: UInt64
292 city64withseed(Float32 NULL, Int16 NULL) :: UInt64 NULL
293 city64withseed(Float32, Int32) :: UInt64
294 city64withseed(Float32 NULL, Int32 NULL) :: UInt64 NULL
295 city64withseed(Float32, Int64) :: UInt64
296 city64withseed(Float32 NULL, Int64 NULL) :: UInt64 NULL
297 city64withseed(Float32, Float32) :: UInt64
298 city64withseed(Float32 NULL, Float32 NULL) :: UInt64 NULL
299 city64withseed(Float32, Float64) :: UInt64
300 city64withseed(Float32 NULL, Float64 NULL) :: UInt64 NULL
301 city64withseed(Float64, UInt8) :: UInt64
302 city64withseed(Float64 NULL, UInt8 NULL) :: UInt64 NULL
303 city64withseed(Float64, UInt16) :: UInt64
304 city64withseed(Float64 NULL, UInt16 NULL) :: UInt64 NULL
305 city64withseed(Float64, UInt32) :: UInt64
306 city64withseed(Float64 NULL, UInt32 NULL) :: UInt64 NULL
307 city64withseed(Float64, UInt64) :: UInt64
308 city64withseed(Float64 NULL, UInt64 NULL) :: UInt64 NULL
309 city64withseed(Float64, Int8) :: UInt64
310 city64withseed(Float64 NULL, Int8 NULL) :: UInt64 NULL
311 city64withseed(Float64, Int16) :: UInt64
312 city64withseed(Float64 NULL, Int16 NULL) :: UInt64 NULL
313 city64withseed(Float64, Int32) :: UInt64
314 city64withseed(Float64 NULL, Int32 NULL) :: UInt64 NULL
315 city64withseed(Float64, Int64) :: UInt64
316 city64withseed(Float64 NULL, Int64 NULL) :: UInt64 NULL
317 city64withseed(Float64, Float32) :: UInt64
318 city64withseed(Float64 NULL, Float32 NULL) :: UInt64 NULL
319 city64withseed(Float64, Float64) :: UInt64
320 city64withseed(Float64 NULL, Float64 NULL) :: UInt64 NULL
0 concat(Variant, Variant) :: Variant
1 concat(Variant NULL, Variant NULL) :: Variant NULL
2 concat FACTORY
3 concat FACTORY
0 concat_ws FACTORY
1 concat_ws FACTORY
0 contains(Array(UInt8), UInt8) :: Boolean
1 contains(Array(UInt8) NULL, UInt8 NULL) :: Boolean NULL
2 contains(Array(UInt16), UInt16) :: Boolean
3 contains(Array(UInt16) NULL, UInt16 NULL) :: Boolean NULL
4 contains(Array(UInt32), UInt32) :: Boolean
5 contains(Array(UInt32) NULL, UInt32 NULL) :: Boolean NULL
6 contains(Array(UInt64), UInt64) :: Boolean
7 contains(Array(UInt64) NULL, UInt64 NULL) :: Boolean NULL
8 contains(Array(Int8), Int8) :: Boolean
9 contains(Array(Int8) NULL, Int8 NULL) :: Boolean NULL
10 contains(Array(Int16), Int16) :: Boolean
11 contains(Array(Int16) NULL, Int16 NULL) :: Boolean NULL
12 contains(Array(Int32), Int32) :: Boolean
13 contains(Array(Int32) NULL, Int32 NULL) :: Boolean NULL
14 contains(Array(Int64), Int64) :: Boolean
15 contains(Array(Int64) NULL, Int64 NULL) :: Boolean NULL
16 contains(Array(Float32), Float32) :: Boolean
17 contains(Array(Float32) NULL, Float32 NULL) :: Boolean NULL
18 contains(Array(Float64), Float64) :: Boolean
19 contains(Array(Float64) NULL, Float64 NULL) :: Boolean NULL
20 contains(Array(String), String) :: Boolean
21 contains(Array(String) NULL, String NULL) :: Boolean NULL
22 contains(Array(Date), Date) :: Boolean
23 contains(Array(Date) NULL, Date NULL) :: Boolean NULL
24 contains(Array(Timestamp), Timestamp) :: Boolean
25 contains(Array(Timestamp) NULL, Timestamp NULL) :: Boolean NULL
26 contains(Array(Boolean), Boolean) :: Boolean
27 contains(Array(Boolean) NULL, Boolean NULL) :: Boolean NULL
28 contains(Array(T0) NULL, T0) :: Boolean
0 convert_timezone(String, Timestamp) :: Timestamp
1 convert_timezone(String NULL, Timestamp NULL) :: Timestamp NULL
0 cos(Float64) :: Float64
1 cos(Float64 NULL) :: Float64 NULL
0 cosine_distance(Array(Float32), Array(Float32)) :: Float32
1 cosine_distance(Array(Float32) NULL, Array(Float32) NULL) :: Float32 NULL
2 cosine_distance(Array(Float64), Array(Float64)) :: Float64
3 cosine_distance(Array(Float64) NULL, Array(Float64) NULL) :: Float64 NULL
0 cot(Float64) :: Float64
1 cot(Float64 NULL) :: Float64 NULL
0 crc32(String) :: UInt32
1 crc32(String NULL) :: UInt32 NULL
0 dayofweek(Date) :: UInt8
1 dayofweek(Date NULL) :: UInt8 NULL
2 dayofweek(Timestamp) :: UInt8
3 dayofweek(Timestamp NULL) :: UInt8 NULL
0 degrees(Float64) :: Float64
1 degrees(Float64 NULL) :: Float64 NULL
0 delete_by_keypath FACTORY
0 diff_days(Date, Date) :: Int64
1 diff_days(Date NULL, Date NULL) :: Int64 NULL
2 diff_days(Timestamp, Timestamp) :: Int64
3 diff_days(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_hours(Timestamp, Timestamp) :: Int64
1 diff_hours(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_isoyears(Date, Date) :: Int64
1 diff_isoyears(Date NULL, Date NULL) :: Int64 NULL
2 diff_isoyears(Timestamp, Timestamp) :: Int64
3 diff_isoyears(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_microseconds(Date, Date) :: Int64
1 diff_microseconds(Date NULL, Date NULL) :: Int64 NULL
2 diff_microseconds(Timestamp, Timestamp) :: Int64
3 diff_microseconds(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_millenniums(Date, Date) :: Int64
1 diff_millenniums(Date NULL, Date NULL) :: Int64 NULL
2 diff_millenniums(Timestamp, Timestamp) :: Int64
3 diff_millenniums(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_minutes(Timestamp, Timestamp) :: Int64
1 diff_minutes(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_months(Date, Date) :: Int64
1 diff_months(Date NULL, Date NULL) :: Int64 NULL
2 diff_months(Timestamp, Timestamp) :: Int64
3 diff_months(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_quarters(Date, Date) :: Int64
1 diff_quarters(Date NULL, Date NULL) :: Int64 NULL
2 diff_quarters(Timestamp, Timestamp) :: Int64
3 diff_quarters(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_seconds(Timestamp, Timestamp) :: Int64
1 diff_seconds(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_weeks(Date, Date) :: Int64
1 diff_weeks(Date NULL, Date NULL) :: Int64 NULL
2 diff_weeks(Timestamp, Timestamp) :: Int64
3 diff_weeks(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_years(Date, Date) :: Int64
1 diff_years(Date NULL, Date NULL) :: Int64 NULL
2 diff_years(Timestamp, Timestamp) :: Int64
3 diff_years(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 diff_yearweeks(Date, Date) :: Int64
1 diff_yearweeks(Date NULL, Date NULL) :: Int64 NULL
2 diff_yearweeks(Timestamp, Timestamp) :: Int64
3 diff_yearweeks(Timestamp NULL, Timestamp NULL) :: Int64 NULL
0 div(UInt8, UInt8) :: UInt8
1 div(UInt8 NULL, UInt8 NULL) :: UInt8 NULL
2 div(UInt8, UInt16) :: UInt16
3 div(UInt8 NULL, UInt16 NULL) :: UInt16 NULL
4 div(UInt8, UInt32) :: UInt32
5 div(UInt8 NULL, UInt32 NULL) :: UInt32 NULL
6 div(UInt8, UInt64) :: UInt64
7 div(UInt8 NULL, UInt64 NULL) :: UInt64 NULL
8 div(UInt8, Int8) :: Int8
9 div(UInt8 NULL, Int8 NULL) :: Int8 NULL
10 div(UInt8, Int16) :: Int16
11 div(UInt8 NULL, Int16 NULL) :: Int16 NULL
12 div(UInt8, Int32) :: Int32
13 div(UInt8 NULL, Int32 NULL) :: Int32 NULL
14 div(UInt8, Int64) :: Int64
15 div(UInt8 NULL, Int64 NULL) :: Int64 NULL
16 div(UInt16, UInt8) :: UInt16
17 div(UInt16 NULL, UInt8 NULL) :: UInt16 NULL
18 div(UInt16, UInt16) :: UInt16
19 div(UInt16 NULL, UInt16 NULL) :: UInt16 NULL
20 div(UInt16, UInt32) :: UInt32
21 div(UInt16 NULL, UInt32 NULL) :: UInt32 NULL
22 div(UInt16, UInt64) :: UInt64
23 div(UInt16 NULL, UInt64 NULL) :: UInt64 NULL
24 div(UInt16, Int8) :: Int16
25 div(UInt16 NULL, Int8 NULL) :: Int16 NULL
26 div(UInt16, Int16) :: Int16
27 div(UInt16 NULL, Int16 NULL) :: Int16 NULL
28 div(UInt16, Int32) :: Int32
29 div(UInt16 NULL, Int32 NULL) :: Int32 NULL
30 div(UInt16, Int64) :: Int64
31 div(UInt16 NULL, Int64 NULL) :: Int64 NULL
32 div(UInt32, UInt8) :: UInt32
33 div(UInt32 NULL, UInt8 NULL) :: UInt32 NULL
34 div(UInt32, UInt16) :: UInt32
35 div(UInt32 NULL, UInt16 NULL) :: UInt32 NULL
36 div(UInt32, UInt32) :: UInt32
37 div(UInt32 NULL, UInt32 NULL) :: UInt32 NULL
38 div(UInt32, UInt64) :: UInt64
39 div(UInt32 NULL, UInt64 NULL) :: UInt64 NULL
40 div(UInt32, Int8) :: Int32
41 div(UInt32 NULL, Int8 NULL) :: Int32 NULL
42 div(UInt32, Int16) :: Int32
43 div(UInt32 NULL, Int16 NULL) :: Int32 NULL
44 div(UInt32, Int32) :: Int32
45 div(UInt32 NULL, Int32 NULL) :: Int32 NULL
46 div(UInt32, Int64) :: Int64
47 div(UInt32 NULL, Int64 NULL) :: Int64 NULL
48 div(UInt64, UInt8) :: UInt64
49 div(UInt64 NULL, UInt8 NULL) :: UInt64 NULL
50 div(UInt64, UInt16) :: UInt64
51 div(UInt64 NULL, UInt16 NULL) :: UInt64 NULL
52 div(UInt64, UInt32) :: UInt64
53 div(UInt64 NULL, UInt32 NULL) :: UInt64 NULL
54 div(UInt64, UInt64) :: UInt64
55 div(UInt64 NULL, UInt64 NULL) :: UInt64 NULL
56 div(UInt64, Int8) :: Int64
57 div(UInt64 NULL, Int8 NULL) :: Int64 NULL
58 div(UInt64, Int16) :: Int64
59 div(UInt64 NULL, Int16 NULL) :: Int64 NULL
60 div(UInt64, Int32) :: Int64
61 div(UInt64 NULL, Int32 NULL) :: Int64 NULL
62 div(UInt64, Int64) :: Int64
63 div(UInt64 NULL, Int64 NULL) :: Int64 NULL
64 div(Int8, UInt8) :: Int8
65 div(Int8 NULL, UInt8 NULL) :: Int8 NULL
66 div(Int8, UInt16) :: Int16
67 div(Int8 NULL, UInt16 NULL) :: Int16 NULL
68 div(Int8, UInt32) :: Int32
69 div(Int8 NULL, UInt32 NULL) :: Int32 NULL
70 div(Int8, UInt64) :: Int64
71 div(Int8 NULL, UInt64 NULL) :: Int64 NULL
72 div(Int8, Int8) :: Int8
73 div(Int8 NULL, Int8 NULL) :: Int8 NULL
74 div(Int8, Int16) :: Int16
75 div(Int8 NULL, Int16 NULL) :: Int16 NULL
76 div(Int8, Int32) :: Int32
77 div(Int8 NULL, Int32 NULL) :: Int32 NULL
78 div(Int8, Int64) :: Int64
79 div(Int8 NULL, Int64 NULL) :: Int64 NULL
80 div(Int16, UInt8) :: Int16
81 div(Int16 NULL, UInt8 NULL) :: Int16 NULL
82 div(Int16, UInt16) :: Int16
83 div(Int16 NULL, UInt16 NULL) :: Int16 NULL
84 div(Int16, UInt32) :: Int32
85 div(Int16 NULL, UInt32 NULL) :: Int32 NULL
86 div(Int16, UInt64) :: Int64
87 div(Int16 NULL, UInt64 NULL) :: Int64 NULL
88 div(Int16, Int8) :: Int16
89 div(Int16 NULL, Int8 NULL) :: Int16 NULL
90 div(Int16, Int16) :: Int16
91 div(Int16 NULL, Int16 NULL) :: Int16 NULL
92 div(Int16, Int32) :: Int32
93 div(Int16 NULL, Int32 NULL) :: Int32 NULL
94 div(Int16, Int64) :: Int64
95 div(Int16 NULL, Int64 NULL) :: Int64 NULL
96 div(Int32, UInt8) :: Int32
97 div(Int32 NULL, UInt8 NULL) :: Int32 NULL
98 div(Int32, UInt16) :: Int32
99 div(Int32 NULL, UInt16 NULL) :: Int32 NULL
100 div(Int32, UInt32) :: Int32
101 div(Int32 NULL, UInt32 NULL) :: Int32 NULL
102 div(Int32, UInt64) :: Int64
103 div(Int32 NULL, UInt64 NULL) :: Int64 NULL
104 div(Int32, Int8) :: Int32
105 div(Int32 NULL, Int8 NULL) :: Int32 NULL
106 div(Int32, Int16) :: Int32
107 div(Int32 NULL, Int16 NULL) :: Int32 NULL
108 div(Int32, Int32) :: Int32
109 div(Int32 NULL, Int32 NULL) :: Int32 NULL
110 div(Int32, Int64) :: Int64
111 div(Int32 NULL, Int64 NULL) :: Int64 NULL
112 div(Int64, UInt8) :: Int64
113 div(Int64 NULL, UInt8 NULL) :: Int64 NULL
114 div(Int64, UInt16) :: Int64
115 div(Int64 NULL, UInt16 NULL) :: Int64 NULL
116 div(Int64, UInt32) :: Int64
117 div(Int64 NULL, UInt32 NULL) :: Int64 NULL
118 div(Int64, UInt64) :: Int64
119 div(Int64 NULL, UInt64 NULL) :: Int64 NULL
120 div(Int64, Int8) :: Int64
121 div(Int64 NULL, Int8 NULL) :: Int64 NULL
122 div(Int64, Int16) :: Int64
123 div(Int64 NULL, Int16 NULL) :: Int64 NULL
124 div(Int64, Int32) :: Int64
125 div(Int64 NULL, Int32 NULL) :: Int64 NULL
126 div(Int64, Int64) :: Int64
127 div(Int64 NULL, Int64 NULL) :: Int64 NULL
128 div(UInt8, Float32) :: Int32
129 div(UInt8 NULL, Float32 NULL) :: Int32 NULL
130 div(UInt8, Float64) :: Int64
131 div(UInt8 NULL, Float64 NULL) :: Int64 NULL
132 div(UInt16, Float32) :: Int32
133 div(UInt16 NULL, Float32 NULL) :: Int32 NULL
134 div(UInt16, Float64) :: Int64
135 div(UInt16 NULL, Float64 NULL) :: Int64 NULL
136 div(UInt32, Float32) :: Int32
137 div(UInt32 NULL, Float32 NULL) :: Int32 NULL
138 div(UInt32, Float64) :: Int64
139 div(UInt32 NULL, Float64 NULL) :: Int64 NULL
140 div(UInt64, Float32) :: Int64
141 div(UInt64 NULL, Float32 NULL) :: Int64 NULL
142 div(UInt64, Float64) :: Int64
143 div(UInt64 NULL, Float64 NULL) :: Int64 NULL
144 div(Int8, Float32) :: Int32
145 div(Int8 NULL, Float32 NULL) :: Int32 NULL
146 div(Int8, Float64) :: Int64
147 div(Int8 NULL, Float64 NULL) :: Int64 NULL
148 div(Int16, Float32) :: Int32
149 div(Int16 NULL, Float32 NULL) :: Int32 NULL
150 div(Int16, Float64) :: Int64
151 div(Int16 NULL, Float64 NULL) :: Int64 NULL
152 div(Int32, Float32) :: Int32
153 div(Int32 NULL, Float32 NULL) :: Int32 NULL
154 div(Int32, Float64) :: Int64
155 div(Int32 NULL, Float64 NULL) :: Int64 NULL
156 div(Int64, Float32) :: Int64
157 div(Int64 NULL, Float32 NULL) :: Int64 NULL
158 div(Int64, Float64) :: Int64
159 div(Int64 NULL, Float64 NULL) :: Int64 NULL
160 div(Float32, UInt8) :: Int32
161 div(Float32 NULL, UInt8 NULL) :: Int32 NULL
162 div(Float32, UInt16) :: Int32
163 div(Float32 NULL, UInt16 NULL) :: Int32 NULL
164 div(Float32, UInt32) :: Int32
165 div(Float32 NULL, UInt32 NULL) :: Int32 NULL
166 div(Float32, UInt64) :: Int64
167 div(Float32 NULL, UInt64 NULL) :: Int64 NULL
168 div(Float32, Int8) :: Int32
169 div(Float32 NULL, Int8 NULL) :: Int32 NULL
170 div(Float32, Int16) :: Int32
171 div(Float32 NULL, Int16 NULL) :: Int32 NULL
172 div(Float32, Int32) :: Int32
173 div(Float32 NULL, Int32 NULL) :: Int32 NULL
174 div(Float32, Int64) :: Int64
175 div(Float32 NULL, Int64 NULL) :: Int64 NULL
176 div(Float64, UInt8) :: Int64
177 div(Float64 NULL, UInt8 NULL) :: Int64 NULL
178 div(Float64, UInt16) :: Int64
179 div(Float64 NULL, UInt16 NULL) :: Int64 NULL
180 div(Float64, UInt32) :: Int64
181 div(Float64 NULL, UInt32 NULL) :: Int64 NULL
182 div(Float64, UInt64) :: Int64
183 div(Float64 NULL, UInt64 NULL) :: Int64 NULL
184 div(Float64, Int8) :: Int64
185 div(Float64 NULL, Int8 NULL) :: Int64 NULL
186 div(Float64, Int16) :: Int64
187 div(Float64 NULL, Int16 NULL) :: Int64 NULL
188 div(Float64, Int32) :: Int64
189 div(Float64 NULL, Int32 NULL) :: Int64 NULL
190 div(Float64, Int64) :: Int64
191 div(Float64 NULL, Int64 NULL) :: Int64 NULL
192 div(Float32, Float32) :: Int32
193 div(Float32 NULL, Float32 NULL) :: Int32 NULL
194 div(Float32, Float64) :: Int64
195 div(Float32 NULL, Float64 NULL) :: Int64 NULL
196 div(Float64, Float32) :: Int64
197 div(Float64 NULL, Float32 NULL) :: Int64 NULL
198 div(Float64, Float64) :: Int64
199 div(Float64 NULL, Float64 NULL) :: Int64 NULL
0 div0(Float64, Float64) :: Float64
1 div0(Float64 NULL, Float64 NULL) :: Float64 NULL
0 divide FACTORY
1 divide(UInt8, UInt8) :: Float64
2 divide(UInt8 NULL, UInt8 NULL) :: Float64 NULL
3 divide(UInt8, UInt16) :: Float64
4 divide(UInt8 NULL, UInt16 NULL) :: Float64 NULL
5 divide(UInt8, UInt32) :: Float64
6 divide(UInt8 NULL, UInt32 NULL) :: Float64 NULL
7 divide(UInt8, UInt64) :: Float64
8 divide(UInt8 NULL, UInt64 NULL) :: Float64 NULL
9 divide(UInt8, Int8) :: Float64
10 divide(UInt8 NULL, Int8 NULL) :: Float64 NULL
11 divide(UInt8, Int16) :: Float64
12 divide(UInt8 NULL, Int16 NULL) :: Float64 NULL
13 divide(UInt8, Int32) :: Float64
14 divide(UInt8 NULL, Int32 NULL) :: Float64 NULL
15 divide(UInt8, Int64) :: Float64
16 divide(UInt8 NULL, Int64 NULL) :: Float64 NULL
17 divide(UInt16, UInt8) :: Float64
18 divide(UInt16 NULL, UInt8 NULL) :: Float64 NULL
19 divide(UInt16, UInt16) :: Float64
20 divide(UInt16 NULL, UInt16 NULL) :: Float64 NULL
21 divide(UInt16, UInt32) :: Float64
22 divide(UInt16 NULL, UInt32 NULL) :: Float64 NULL
23 divide(UInt16, UInt64) :: Float64
24 divide(UInt16 NULL, UInt64 NULL) :: Float64 NULL
25 divide(UInt16, Int8) :: Float64
26 divide(UInt16 NULL, Int8 NULL) :: Float64 NULL
27 divide(UInt16, Int16) :: Float64
28 divide(UInt16 NULL, Int16 NULL) :: Float64 NULL
29 divide(UInt16, Int32) :: Float64
30 divide(UInt16 NULL, Int32 NULL) :: Float64 NULL
31 divide(UInt16, Int64) :: Float64
32 divide(UInt16 NULL, Int64 NULL) :: Float64 NULL
33 divide(UInt32, UInt8) :: Float64
34 divide(UInt32 NULL, UInt8 NULL) :: Float64 NULL
35 divide(UInt32, UInt16) :: Float64
36 divide(UInt32 NULL, UInt16 NULL) :: Float64 NULL
37 divide(UInt32, UInt32) :: Float64
38 divide(UInt32 NULL, UInt32 NULL) :: Float64 NULL
39 divide(UInt32, UInt64) :: Float64
40 divide(UInt32 NULL, UInt64 NULL) :: Float64 NULL
41 divide(UInt32, Int8) :: Float64
42 divide(UInt32 NULL, Int8 NULL) :: Float64 NULL
43 divide(UInt32, Int16) :: Float64
44 divide(UInt32 NULL, Int16 NULL) :: Float64 NULL
45 divide(UInt32, Int32) :: Float64
46 divide(UInt32 NULL, Int32 NULL) :: Float64 NULL
47 divide(UInt32, Int64) :: Float64
48 divide(UInt32 NULL, Int64 NULL) :: Float64 NULL
49 divide(UInt64, UInt8) :: Float64
50 divide(UInt64 NULL, UInt8 NULL) :: Float64 NULL
51 divide(UInt64, UInt16) :: Float64
52 divide(UInt64 NULL, UInt16 NULL) :: Float64 NULL
53 divide(UInt64, UInt32) :: Float64
54 divide(UInt64 NULL, UInt32 NULL) :: Float64 NULL
55 divide(UInt64, UInt64) :: Float64
56 divide(UInt64 NULL, UInt64 NULL) :: Float64 NULL
57 divide(UInt64, Int8) :: Float64
58 divide(UInt64 NULL, Int8 NULL) :: Float64 NULL
59 divide(UInt64, Int16) :: Float64
60 divide(UInt64 NULL, Int16 NULL) :: Float64 NULL
61 divide(UInt64, Int32) :: Float64
62 divide(UInt64 NULL, Int32 NULL) :: Float64 NULL
63 divide(UInt64, Int64) :: Float64
64 divide(UInt64 NULL, Int64 NULL) :: Float64 NULL
65 divide(Int8, UInt8) :: Float64
66 divide(Int8 NULL, UInt8 NULL) :: Float64 NULL
67 divide(Int8, UInt16) :: Float64
68 divide(Int8 NULL, UInt16 NULL) :: Float64 NULL
69 divide(Int8, UInt32) :: Float64
70 divide(Int8 NULL, UInt32 NULL) :: Float64 NULL
71 divide(Int8, UInt64) :: Float64
72 divide(Int8 NULL, UInt64 NULL) :: Float64 NULL
73 divide(Int8, Int8) :: Float64
74 divide(Int8 NULL, Int8 NULL) :: Float64 NULL
75 divide(Int8, Int16) :: Float64
76 divide(Int8 NULL, Int16 NULL) :: Float64 NULL
77 divide(Int8, Int32) :: Float64
78 divide(Int8 NULL, Int32 NULL) :: Float64 NULL
79 divide(Int8, Int64) :: Float64
80 divide(Int8 NULL, Int64 NULL) :: Float64 NULL
81 divide(Int16, UInt8) :: Float64
82 divide(Int16 NULL, UInt8 NULL) :: Float64 NULL
83 divide(Int16, UInt16) :: Float64
84 divide(Int16 NULL, UInt16 NULL) :: Float64 NULL
85 divide(Int16, UInt32) :: Float64
86 divide(Int16 NULL, UInt32 NULL) :: Float64 NULL
87 divide(Int16, UInt64) :: Float64
88 divide(Int16 NULL, UInt64 NULL) :: Float64 NULL
89 divide(Int16, Int8) :: Float64
90 divide(Int16 NULL, Int8 NULL) :: Float64 NULL
91 divide(Int16, Int16) :: Float64
92 divide(Int16 NULL, Int16 NULL) :: Float64 NULL
93 divide(Int16, Int32) :: Float64
94 divide(Int16 NULL, Int32 NULL) :: Float64 NULL
95 divide(Int16, Int64) :: Float64
96 divide(Int16 NULL, Int64 NULL) :: Float64 NULL
97 divide(Int32, UInt8) :: Float64
98 divide(Int32 NULL, UInt8 NULL) :: Float64 NULL
99 divide(Int32, UInt16) :: Float64
100 divide(Int32 NULL, UInt16 NULL) :: Float64 NULL
101 divide(Int32, UInt32) :: Float64
102 divide(Int32 NULL, UInt32 NULL) :: Float64 NULL
103 divide(Int32, UInt64) :: Float64
104 divide(Int32 NULL, UInt64 NULL) :: Float64 NULL
105 divide(Int32, Int8) :: Float64
106 divide(Int32 NULL, Int8 NULL) :: Float64 NULL
107 divide(Int32, Int16) :: Float64
108 divide(Int32 NULL, Int16 NULL) :: Float64 NULL
109 divide(Int32, Int32) :: Float64
110 divide(Int32 NULL, Int32 NULL) :: Float64 NULL
111 divide(Int32, Int64) :: Float64
112 divide(Int32 NULL, Int64 NULL) :: Float64 NULL
113 divide(Int64, UInt8) :: Float64
114 divide(Int64 NULL, UInt8 NULL) :: Float64 NULL
115 divide(Int64, UInt16) :: Float64
116 divide(Int64 NULL, UInt16 NULL) :: Float64 NULL
117 divide(Int64, UInt32) :: Float64
118 divide(Int64 NULL, UInt32 NULL) :: Float64 NULL
119 divide(Int64, UInt64) :: Float64
120 divide(Int64 NULL, UInt64 NULL) :: Float64 NULL
121 divide(Int64, Int8) :: Float64
122 divide(Int64 NULL, Int8 NULL) :: Float64 NULL
123 divide(Int64, Int16) :: Float64
124 divide(Int64 NULL, Int16 NULL) :: Float64 NULL
125 divide(Int64, Int32) :: Float64
126 divide(Int64 NULL, Int32 NULL) :: Float64 NULL
127 divide(Int64, Int64) :: Float64
128 divide(Int64 NULL, Int64 NULL) :: Float64 NULL
129 divide(UInt8, Float32) :: Float64
130 divide(UInt8 NULL, Float32 NULL) :: Float64 NULL
131 divide(UInt8, Float64) :: Float64
132 divide(UInt8 NULL, Float64 NULL) :: Float64 NULL
133 divide(UInt16, Float32) :: Float64
134 divide(UInt16 NULL, Float32 NULL) :: Float64 NULL
135 divide(UInt16, Float64) :: Float64
136 divide(UInt16 NULL, Float64 NULL) :: Float64 NULL
137 divide(UInt32, Float32) :: Float64
138 divide(UInt32 NULL, Float32 NULL) :: Float64 NULL
139 divide(UInt32, Float64) :: Float64
140 divide(UInt32 NULL, Float64 NULL) :: Float64 NULL
141 divide(UInt64, Float32) :: Float64
142 divide(UInt64 NULL, Float32 NULL) :: Float64 NULL
143 divide(UInt64, Float64) :: Float64
144 divide(UInt64 NULL, Float64 NULL) :: Float64 NULL
145 divide(Int8, Float32) :: Float64
146 divide(Int8 NULL, Float32 NULL) :: Float64 NULL
147 divide(Int8, Float64) :: Float64
148 divide(Int8 NULL, Float64 NULL) :: Float64 NULL
149 divide(Int16, Float32) :: Float64
150 divide(Int16 NULL, Float32 NULL) :: Float64 NULL
151 divide(Int16, Float64) :: Float64
152 divide(Int16 NULL, Float64 NULL) :: Float64 NULL
153 divide(Int32, Float32) :: Float64
154 divide(Int32 NULL, Float32 NULL) :: Float64 NULL
155 divide(Int32, Float64) :: Float64
156 divide(Int32 NULL, Float64 NULL) :: Float64 NULL
157 divide(Int64, Float32) :: Float64
158 divide(Int64 NULL, Float32 NULL) :: Float64 NULL
159 divide(Int64, Float64) :: Float64
160 divide(Int64 NULL, Float64 NULL) :: Float64 NULL
161 divide(Float32, UInt8) :: Float64
162 divide(Float32 NULL, UInt8 NULL) :: Float64 NULL
163 divide(Float32, UInt16) :: Float64
164 divide(Float32 NULL, UInt16 NULL) :: Float64 NULL
165 divide(Float32, UInt32) :: Float64
166 divide(Float32 NULL, UInt32 NULL) :: Float64 NULL
167 divide(Float32, UInt64) :: Float64
168 divide(Float32 NULL, UInt64 NULL) :: Float64 NULL
169 divide(Float32, Int8) :: Float64
170 divide(Float32 NULL, Int8 NULL) :: Float64 NULL
171 divide(Float32, Int16) :: Float64
172 divide(Float32 NULL, Int16 NULL) :: Float64 NULL
173 divide(Float32, Int32) :: Float64
174 divide(Float32 NULL, Int32 NULL) :: Float64 NULL
175 divide(Float32, Int64) :: Float64
176 divide(Float32 NULL, Int64 NULL) :: Float64 NULL
177 divide(Float64, UInt8) :: Float64
178 divide(Float64 NULL, UInt8 NULL) :: Float64 NULL
179 divide(Float64, UInt16) :: Float64
180 divide(Float64 NULL, UInt16 NULL) :: Float64 NULL
181 divide(Float64, UInt32) :: Float64
182 divide(Float64 NULL, UInt32 NULL) :: Float64 NULL
183 divide(Float64, UInt64) :: Float64
184 divide(Float64 NULL, UInt64 NULL) :: Float64 NULL
185 divide(Float64, Int8) :: Float64
186 divide(Float64 NULL, Int8 NULL) :: Float64 NULL
187 divide(Float64, Int16) :: Float64
188 divide(Float64 NULL, Int16 NULL) :: Float64 NULL
189 divide(Float64, Int32) :: Float64
190 divide(Float64 NULL, Int32 NULL) :: Float64 NULL
191 divide(Float64, Int64) :: Float64
192 divide(Float64 NULL, Int64 NULL) :: Float64 NULL
193 divide(Float32, Float32) :: Float64
194 divide(Float32 NULL, Float32 NULL) :: Float64 NULL
195 divide(Float32, Float64) :: Float64
196 divide(Float32 NULL, Float64 NULL) :: Float64 NULL
197 divide(Float64, Float32) :: Float64
198 divide(Float64 NULL, Float32 NULL) :: Float64 NULL
199 divide(Float64, Float64) :: Float64
200 divide(Float64 NULL, Float64 NULL) :: Float64 NULL
0 divnull(Float64 NULL, Float64 NULL) :: Float64 NULL
0 epoch(Timestamp) :: Float64
1 epoch(Timestamp NULL) :: Float64 NULL
2 epoch(Interval) :: Float64
3 epoch(Interval NULL) :: Float64 NULL
0 eq(Variant, Variant) :: Boolean
1 eq(Variant NULL, Variant NULL) :: Boolean NULL
2 eq(String, String) :: Boolean
3 eq(String NULL, String NULL) :: Boolean NULL
4 eq(Date, Date) :: Boolean
5 eq(Date NULL, Date NULL) :: Boolean NULL
6 eq(Timestamp, Timestamp) :: Boolean
7 eq(Timestamp NULL, Timestamp NULL) :: Boolean NULL
8 eq(UInt8, UInt8) :: Boolean
9 eq(UInt8 NULL, UInt8 NULL) :: Boolean NULL
10 eq(Int8, Int8) :: Boolean
11 eq(Int8 NULL, Int8 NULL) :: Boolean NULL
12 eq(UInt16, UInt16) :: Boolean
13 eq(UInt16 NULL, UInt16 NULL) :: Boolean NULL
14 eq(Int16, Int16) :: Boolean
15 eq(Int16 NULL, Int16 NULL) :: Boolean NULL
16 eq(UInt32, UInt32) :: Boolean
17 eq(UInt32 NULL, UInt32 NULL) :: Boolean NULL
18 eq(Int32, Int32) :: Boolean
19 eq(Int32 NULL, Int32 NULL) :: Boolean NULL
20 eq(UInt64, UInt64) :: Boolean
21 eq(UInt64 NULL, UInt64 NULL) :: Boolean NULL
22 eq(Int64, Int64) :: Boolean
23 eq(Int64 NULL, Int64 NULL) :: Boolean NULL
24 eq FACTORY
25 eq(Float32, Float32) :: Boolean
26 eq(Float32 NULL, Float32 NULL) :: Boolean NULL
27 eq(Float64, Float64) :: Boolean
28 eq(Float64 NULL, Float64 NULL) :: Boolean NULL
29 eq(String, UInt8) :: Boolean
30 eq(String NULL, UInt8 NULL) :: Boolean NULL
31 eq(UInt8, String) :: Boolean
32 eq(UInt8 NULL, String NULL) :: Boolean NULL
33 eq(String, UInt16) :: Boolean
34 eq(String NULL, UInt16 NULL) :: Boolean NULL
35 eq(UInt16, String) :: Boolean
36 eq(UInt16 NULL, String NULL) :: Boolean NULL
37 eq(String, UInt32) :: Boolean
38 eq(String NULL, UInt32 NULL) :: Boolean NULL
39 eq(UInt32, String) :: Boolean
40 eq(UInt32 NULL, String NULL) :: Boolean NULL
41 eq(String, UInt64) :: Boolean
42 eq(String NULL, UInt64 NULL) :: Boolean NULL
43 eq(UInt64, String) :: Boolean
44 eq(UInt64 NULL, String NULL) :: Boolean NULL
45 eq(String, Int8) :: Boolean
46 eq(String NULL, Int8 NULL) :: Boolean NULL
47 eq(Int8, String) :: Boolean
48 eq(Int8 NULL, String NULL) :: Boolean NULL
49 eq(String, Int16) :: Boolean
50 eq(String NULL, Int16 NULL) :: Boolean NULL
51 eq(Int16, String) :: Boolean
52 eq(Int16 NULL, String NULL) :: Boolean NULL
53 eq(String, Int32) :: Boolean
54 eq(String NULL, Int32 NULL) :: Boolean NULL
55 eq(Int32, String) :: Boolean
56 eq(Int32 NULL, String NULL) :: Boolean NULL
57 eq(String, Int64) :: Boolean
58 eq(String NULL, Int64 NULL) :: Boolean NULL
59 eq(Int64, String) :: Boolean
60 eq(Int64 NULL, String NULL) :: Boolean NULL
61 eq(String, Float32) :: Boolean
62 eq(String NULL, Float32 NULL) :: Boolean NULL
63 eq(Float32, String) :: Boolean
64 eq(Float32 NULL, String NULL) :: Boolean NULL
65 eq(String, Float64) :: Boolean
66 eq(String NULL, Float64 NULL) :: Boolean NULL
67 eq(Float64, String) :: Boolean
68 eq(Float64 NULL, String NULL) :: Boolean NULL
69 eq(Boolean, Boolean) :: Boolean
70 eq(Boolean NULL, Boolean NULL) :: Boolean NULL
71 eq(Array(Nothing), Array(Nothing)) :: Boolean
72 eq(Array(Nothing) NULL, Array(Nothing) NULL) :: Boolean NULL
73 eq(Array(T0), Array(T0)) :: Boolean
74 eq(Array(T0) NULL, Array(T0) NULL) :: Boolean NULL
75 eq FACTORY
76 eq(Interval, Interval) :: Boolean
77 eq(Interval NULL, Interval NULL) :: Boolean NULL
0 exp(UInt8) :: Float64
1 exp(UInt8 NULL) :: Float64 NULL
2 exp(UInt16) :: Float64
3 exp(UInt16 NULL) :: Float64 NULL
4 exp(UInt32) :: Float64
5 exp(UInt32 NULL) :: Float64 NULL
6 exp(UInt64) :: Float64
7 exp(UInt64 NULL) :: Float64 NULL
8 exp(Int8) :: Float64
9 exp(Int8 NULL) :: Float64 NULL
10 exp(Int16) :: Float64
11 exp(Int16 NULL) :: Float64 NULL
12 exp(Int32) :: Float64
13 exp(Int32 NULL) :: Float64 NULL
14 exp(Int64) :: Float64
15 exp(Int64 NULL) :: Float64 NULL
16 exp(Float32) :: Float64
17 exp(Float32 NULL) :: Float64 NULL
18 exp(Float64) :: Float64
19 exp(Float64 NULL) :: Float64 NULL
0 factorial(UInt8) :: Int64
1 factorial(UInt8 NULL) :: Int64 NULL
2 factorial(UInt16) :: Int64
3 factorial(UInt16 NULL) :: Int64 NULL
4 factorial(UInt32) :: Int64
5 factorial(UInt32 NULL) :: Int64 NULL
6 factorial(UInt64) :: Int64
7 factorial(UInt64 NULL) :: Int64 NULL
8 factorial(Int8) :: Int64
9 factorial(Int8 NULL) :: Int64 NULL
10 factorial(Int16) :: Int64
11 factorial(Int16 NULL) :: Int64 NULL
12 factorial(Int32) :: Int64
13 factorial(Int32 NULL) :: Int64 NULL
14 factorial(Int64) :: Int64
15 factorial(Int64 NULL) :: Int64 NULL
0 feistel_obfuscate(UInt64, UInt64) :: UInt64
1 feistel_obfuscate(UInt64 NULL, UInt64 NULL) :: UInt64 NULL
2 feistel_obfuscate(Int8, UInt64) :: Int8
3 feistel_obfuscate(Int8 NULL, UInt64 NULL) :: Int8 NULL
4 feistel_obfuscate(Int16, UInt64) :: Int16
5 feistel_obfuscate(Int16 NULL, UInt64 NULL) :: Int16 NULL
6 feistel_obfuscate(Int32, UInt64) :: Int32
7 feistel_obfuscate(Int32 NULL, UInt64 NULL) :: Int32 NULL
8 feistel_obfuscate(Int64, UInt64) :: Int64
9 feistel_obfuscate(Int64 NULL, UInt64 NULL) :: Int64 NULL
10 feistel_obfuscate(Float32, UInt64) :: Float32
11 feistel_obfuscate(Float32 NULL, UInt64 NULL) :: Float32 NULL
12 feistel_obfuscate(Float64, UInt64) :: Float64
13 feistel_obfuscate(Float64 NULL, UInt64 NULL) :: Float64 NULL
0 flatten FACTORY
0 floor FACTORY
1 floor(Float64) :: Float64
2 floor(Float64 NULL) :: Float64 NULL
0 from_base64(String) :: Binary
1 from_base64(String NULL) :: Binary NULL
0 from_hex(String) :: Binary
1 from_hex(String NULL) :: Binary NULL
0 gen_random_uuid() :: String
0 geo_distance(Float64, Float64, Float64, Float64) :: Float32
1 geo_distance(Float64 NULL, Float64 NULL, Float64 NULL, Float64 NULL) :: Float32 NULL
0 geo_to_h3(Float64, Float64, UInt8) :: UInt64
1 geo_to_h3(Float64 NULL, Float64 NULL, UInt8 NULL) :: UInt64 NULL
0 geohash_decode(String) :: Tuple(Float64, Float64)
1 geohash_decode(String NULL) :: Tuple(Float64, Float64) NULL
0 geohash_encode(Float64, Float64) :: String
1 geohash_encode(Float64 NULL, Float64 NULL) :: String NULL
2 geohash_encode(Float64, Float64, UInt8) :: String
3 geohash_encode(Float64 NULL, Float64 NULL, UInt8 NULL) :: String NULL
0 get(Variant, String) :: Variant NULL
1 get(Variant NULL, String NULL) :: Variant NULL
2 get(Variant, Int64) :: Variant NULL
3 get(Variant NULL, Int64 NULL) :: Variant NULL
4 get(Array(Nothing) NULL, UInt64 NULL) :: NULL
5 get(Array(NULL) NULL, UInt64 NULL) :: NULL
6 get(Array(T0 NULL), UInt64) :: T0 NULL
7 get(Array(T0 NULL) NULL, UInt64 NULL) :: T0 NULL
8 get(Map(Nothing) NULL, T0 NULL) :: NULL
9 get(Map(T0, NULL) NULL, T0 NULL) :: NULL
10 get(Map(T0, T1 NULL), T0) :: T1 NULL
11 get(Map(T0, T1 NULL) NULL, T0 NULL) :: T1 NULL
12 get FACTORY
13 get FACTORY
14 get FACTORY
0 get_by_keypath FACTORY
0 get_by_keypath_string FACTORY
0 get_ignore_case(Variant, String) :: Variant NULL
1 get_ignore_case(Variant NULL, String NULL) :: Variant NULL
0 get_path(Variant, String) :: Variant NULL
1 get_path(Variant NULL, String NULL) :: Variant NULL
0 get_string(Variant, String) :: String NULL
1 get_string(Variant NULL, String NULL) :: String NULL
2 get_string(Variant, Int64) :: String NULL
3 get_string(Variant NULL, Int64 NULL) :: String NULL
0 glob(String, String) :: Boolean
1 glob(String NULL, String NULL) :: Boolean NULL
0 great_circle_angle(Float64, Float64, Float64, Float64) :: Float32
1 great_circle_angle(Float64 NULL, Float64 NULL, Float64 NULL, Float64 NULL) :: Float32 NULL
0 great_circle_distance(Float64, Float64, Float64, Float64) :: Float32
1 great_circle_distance(Float64 NULL, Float64 NULL, Float64 NULL, Float64 NULL) :: Float32 NULL
0 grouping FACTORY
0 gt(Variant, Variant) :: Boolean
1 gt(Variant NULL, Variant NULL) :: Boolean NULL
2 gt(String, String) :: Boolean
3 gt(String NULL, String NULL) :: Boolean NULL
4 gt(Date, Date) :: Boolean
5 gt(Date NULL, Date NULL) :: Boolean NULL
6 gt(Timestamp, Timestamp) :: Boolean
7 gt(Timestamp NULL, Timestamp NULL) :: Boolean NULL
8 gt(UInt8, UInt8) :: Boolean
9 gt(UInt8 NULL, UInt8 NULL) :: Boolean NULL
10 gt(Int8, Int8) :: Boolean
11 gt(Int8 NULL, Int8 NULL) :: Boolean NULL
12 gt(UInt16, UInt16) :: Boolean
13 gt(UInt16 NULL, UInt16 NULL) :: Boolean NULL
14 gt(Int16, Int16) :: Boolean
15 gt(Int16 NULL, Int16 NULL) :: Boolean NULL
16 gt(UInt32, UInt32) :: Boolean
17 gt(UInt32 NULL, UInt32 NULL) :: Boolean NULL
18 gt(Int32, Int32) :: Boolean
19 gt(Int32 NULL, Int32 NULL) :: Boolean NULL
20 gt(UInt64, UInt64) :: Boolean
21 gt(UInt64 NULL, UInt64 NULL) :: Boolean NULL
22 gt(Int64, Int64) :: Boolean
23 gt(Int64 NULL, Int64 NULL) :: Boolean NULL
24 gt FACTORY
25 gt(Float32, Float32) :: Boolean
26 gt(Float32 NULL, Float32 NULL) :: Boolean NULL
27 gt(Float64, Float64) :: Boolean
28 gt(Float64 NULL, Float64 NULL) :: Boolean NULL
29 gt(String, UInt8) :: Boolean
30 gt(String NULL, UInt8 NULL) :: Boolean NULL
31 gt(UInt8, String) :: Boolean
32 gt(UInt8 NULL, String NULL) :: Boolean NULL
33 gt(String, UInt16) :: Boolean
34 gt(String NULL, UInt16 NULL) :: Boolean NULL
35 gt(UInt16, String) :: Boolean
36 gt(UInt16 NULL, String NULL) :: Boolean NULL
37 gt(String, UInt32) :: Boolean
38 gt(String NULL, UInt32 NULL) :: Boolean NULL
39 gt(UInt32, String) :: Boolean
40 gt(UInt32 NULL, String NULL) :: Boolean NULL
41 gt(String, UInt64) :: Boolean
42 gt(String NULL, UInt64 NULL) :: Boolean NULL
43 gt(UInt64, String) :: Boolean
44 gt(UInt64 NULL, String NULL) :: Boolean NULL
45 gt(String, Int8) :: Boolean
46 gt(String NULL, Int8 NULL) :: Boolean NULL
47 gt(Int8, String) :: Boolean
48 gt(Int8 NULL, String NULL) :: Boolean NULL
49 gt(String, Int16) :: Boolean
50 gt(String NULL, Int16 NULL) :: Boolean NULL
51 gt(Int16, String) :: Boolean
52 gt(Int16 NULL, String NULL) :: Boolean NULL
53 gt(String, Int32) :: Boolean
54 gt(String NULL, Int32 NULL) :: Boolean NULL
55 gt(Int32, String) :: Boolean
56 gt(Int32 NULL, String NULL) :: Boolean NULL
57 gt(String, Int64) :: Boolean
58 gt(String NULL, Int64 NULL) :: Boolean NULL
59 gt(Int64, String) :: Boolean
60 gt(Int64 NULL, String NULL) :: Boolean NULL
61 gt(String, Float32) :: Boolean
62 gt(String NULL, Float32 NULL) :: Boolean NULL
63 gt(Float32, String) :: Boolean
64 gt(Float32 NULL, String NULL) :: Boolean NULL
65 gt(String, Float64) :: Boolean
66 gt(String NULL, Float64 NULL) :: Boolean NULL
67 gt(Float64, String) :: Boolean
68 gt(Float64 NULL, String NULL) :: Boolean NULL
69 gt(Boolean, Boolean) :: Boolean
70 gt(Boolean NULL, Boolean NULL) :: Boolean NULL
71 gt(Array(Nothing), Array(Nothing)) :: Boolean
72 gt(Array(Nothing) NULL, Array(Nothing) NULL) :: Boolean NULL
73 gt(Array(T0), Array(T0)) :: Boolean
74 gt(Array(T0) NULL, Array(T0) NULL) :: Boolean NULL
75 gt FACTORY
76 gt(Interval, Interval) :: Boolean
77 gt(Interval NULL, Interval NULL) :: Boolean NULL
0 gte(Variant, Variant) :: Boolean
1 gte(Variant NULL, Variant NULL) :: Boolean NULL
2 gte(String, String) :: Boolean
3 gte(String NULL, String NULL) :: Boolean NULL
4 gte(Date, Date) :: Boolean
5 gte(Date NULL, Date NULL) :: Boolean NULL
6 gte(Timestamp, Timestamp) :: Boolean
7 gte(Timestamp NULL, Timestamp NULL) :: Boolean NULL
8 gte(UInt8, UInt8) :: Boolean
9 gte(UInt8 NULL, UInt8 NULL) :: Boolean NULL
10 gte(Int8, Int8) :: Boolean
11 gte(Int8 NULL, Int8 NULL) :: Boolean NULL
12 gte(UInt16, UInt16) :: Boolean
13 gte(UInt16 NULL, UInt16 NULL) :: Boolean NULL
14 gte(Int16, Int16) :: Boolean
15 gte(Int16 NULL, Int16 NULL) :: Boolean NULL
16 gte(UInt32, UInt32) :: Boolean
17 gte(UInt32 NULL, UInt32 NULL) :: Boolean NULL
18 gte(Int32, Int32) :: Boolean
19 gte(Int32 NULL, Int32 NULL) :: Boolean NULL
20 gte(UInt64, UInt64) :: Boolean
21 gte(UInt64 NULL, UInt64 NULL) :: Boolean NULL
22 gte(Int64, Int64) :: Boolean
23 gte(Int64 NULL, Int64 NULL) :: Boolean NULL
24 gte FACTORY
25 gte(Float32, Float32) :: Boolean
26 gte(Float32 NULL, Float32 NULL) :: Boolean NULL
27 gte(Float64, Float64) :: Boolean
28 gte(Float64 NULL, Float64 NULL) :: Boolean NULL
29 gte(String, UInt8) :: Boolean
30 gte(String NULL, UInt8 NULL) :: Boolean NULL
31 gte(UInt8, String) :: Boolean
32 gte(UInt8 NULL, String NULL) :: Boolean NULL
33 gte(String, UInt16) :: Boolean
34 gte(String NULL, UInt16 NULL) :: Boolean NULL
35 gte(UInt16, String) :: Boolean
36 gte(UInt16 NULL, String NULL) :: Boolean NULL
37 gte(String, UInt32) :: Boolean
38 gte(String NULL, UInt32 NULL) :: Boolean NULL
39 gte(UInt32, String) :: Boolean
40 gte(UInt32 NULL, String NULL) :: Boolean NULL
41 gte(String, UInt64) :: Boolean
42 gte(String NULL, UInt64 NULL) :: Boolean NULL
43 gte(UInt64, String) :: Boolean
44 gte(UInt64 NULL, String NULL) :: Boolean NULL
45 gte(String, Int8) :: Boolean
46 gte(String NULL, Int8 NULL) :: Boolean NULL
47 gte(Int8, String) :: Boolean
48 gte(Int8 NULL, String NULL) :: Boolean NULL
49 gte(String, Int16) :: Boolean
50 gte(String NULL, Int16 NULL) :: Boolean NULL
51 gte(Int16, String) :: Boolean
52 gte(Int16 NULL, String NULL) :: Boolean NULL
53 gte(String, Int32) :: Boolean
54 gte(String NULL, Int32 NULL) :: Boolean NULL
55 gte(Int32, String) :: Boolean
56 gte(Int32 NULL, String NULL) :: Boolean NULL
57 gte(String, Int64) :: Boolean
58 gte(String NULL, Int64 NULL) :: Boolean NULL
59 gte(Int64, String) :: Boolean
60 gte(Int64 NULL, String NULL) :: Boolean NULL
61 gte(String, Float32) :: Boolean
62 gte(String NULL, Float32 NULL) :: Boolean NULL
63 gte(Float32, String) :: Boolean
64 gte(Float32 NULL, String NULL) :: Boolean NULL
65 gte(String, Float64) :: Boolean
66 gte(String NULL, Float64 NULL) :: Boolean NULL
67 gte(Float64, String) :: Boolean
68 gte(Float64 NULL, String NULL) :: Boolean NULL
69 gte(Boolean, Boolean) :: Boolean
70 gte(Boolean NULL, Boolean NULL) :: Boolean NULL
71 gte(Array(Nothing), Array(Nothing)) :: Boolean
72 gte(Array(Nothing) NULL, Array(Nothing) NULL) :: Boolean NULL
73 gte(Array(T0), Array(T0)) :: Boolean
74 gte(Array(T0) NULL, Array(T0) NULL) :: Boolean NULL
75 gte FACTORY
76 gte(Interval, Interval) :: Boolean
77 gte(Interval NULL, Interval NULL) :: Boolean NULL
0 h3_cell_area_m2(UInt64) :: Float64
1 h3_cell_area_m2(UInt64 NULL) :: Float64 NULL
0 h3_cell_area_rads2(UInt64) :: Float64
1 h3_cell_area_rads2(UInt64 NULL) :: Float64 NULL
0 h3_distance(UInt64, UInt64) :: Int32
1 h3_distance(UInt64 NULL, UInt64 NULL) :: Int32 NULL
0 h3_edge_angle(UInt8) :: Float64
1 h3_edge_angle(UInt8 NULL) :: Float64 NULL
0 h3_edge_length_km(UInt8) :: Float64
1 h3_edge_length_km(UInt8 NULL) :: Float64 NULL
0 h3_edge_length_m(UInt8) :: Float64
1 h3_edge_length_m(UInt8 NULL) :: Float64 NULL
0 h3_exact_edge_length_km(UInt64) :: Float64
1 h3_exact_edge_length_km(UInt64 NULL) :: Float64 NULL
0 h3_exact_edge_length_m(UInt64) :: Float64
1 h3_exact_edge_length_m(UInt64 NULL) :: Float64 NULL
0 h3_exact_edge_length_rads(UInt64) :: Float64
1 h3_exact_edge_length_rads(UInt64 NULL) :: Float64 NULL
0 h3_get_base_cell(UInt64) :: UInt8
1 h3_get_base_cell(UInt64 NULL) :: UInt8 NULL
0 h3_get_destination_index_from_unidirectional_edge(UInt64) :: UInt64
1 h3_get_destination_index_from_unidirectional_edge(UInt64 NULL) :: UInt64 NULL
0 h3_get_faces(UInt64) :: Array(UInt8)
1 h3_get_faces(UInt64 NULL) :: Array(UInt8) NULL
0 h3_get_indexes_from_unidirectional_edge(UInt64) :: Tuple(UInt64, UInt64)
1 h3_get_indexes_from_unidirectional_edge(UInt64 NULL) :: Tuple(UInt64, UInt64) NULL
0 h3_get_origin_index_from_unidirectional_edge(UInt64) :: UInt64
1 h3_get_origin_index_from_unidirectional_edge(UInt64 NULL) :: UInt64 NULL
0 h3_get_resolution(UInt64) :: UInt8
1 h3_get_resolution(UInt64 NULL) :: UInt8 NULL
0 h3_get_unidirectional_edge(UInt64, UInt64) :: UInt64
1 h3_get_unidirectional_edge(UInt64 NULL, UInt64 NULL) :: UInt64 NULL
0 h3_get_unidirectional_edge_boundary(UInt64) :: Array(Tuple(Float64, Float64))
1 h3_get_unidirectional_edge_boundary(UInt64 NULL) :: Array(Tuple(Float64, Float64)) NULL
0 h3_get_unidirectional_edges_from_hexagon(UInt64) :: Array(UInt64)
1 h3_get_unidirectional_edges_from_hexagon(UInt64 NULL) :: Array(UInt64) NULL
0 h3_hex_area_km2(UInt8) :: Float64
1 h3_hex_area_km2(UInt8 NULL) :: Float64 NULL
0 h3_hex_area_m2(UInt8) :: Float64
1 h3_hex_area_m2(UInt8 NULL) :: Float64 NULL
0 h3_hex_ring(UInt64, UInt32) :: Array(UInt64)
1 h3_hex_ring(UInt64 NULL, UInt32 NULL) :: Array(UInt64) NULL
0 h3_indexes_are_neighbors(UInt64, UInt64) :: Boolean
1 h3_indexes_are_neighbors(UInt64 NULL, UInt64 NULL) :: Boolean NULL
0 h3_is_pentagon(UInt64) :: Boolean
1 h3_is_pentagon(UInt64 NULL) :: Boolean NULL
0 h3_is_res_class_iii(UInt64) :: Boolean
1 h3_is_res_class_iii(UInt64 NULL) :: Boolean NULL
0 h3_is_valid(UInt64) :: Boolean
1 h3_is_valid(UInt64 NULL) :: Boolean NULL
0 h3_k_ring(UInt64, UInt32) :: Array(UInt64)
1 h3_k_ring(UInt64 NULL, UInt32 NULL) :: Array(UInt64) NULL
0 h3_line(UInt64, UInt64) :: Array(UInt64)
1 h3_line(UInt64 NULL, UInt64 NULL) :: Array(UInt64) NULL
0 h3_num_hexagons(UInt8) :: UInt64
1 h3_num_hexagons(UInt8 NULL) :: UInt64 NULL
0 h3_to_center_child(UInt64, UInt8) :: UInt64
1 h3_to_center_child(UInt64 NULL, UInt8 NULL) :: UInt64 NULL
0 h3_to_children(UInt64, UInt8) :: Array(UInt64)
1 h3_to_children(UInt64 NULL, UInt8 NULL) :: Array(UInt64) NULL
0 h3_to_geo(UInt64) :: Tuple(Float64, Float64)
1 h3_to_geo(UInt64 NULL) :: Tuple(Float64, Float64) NULL
0 h3_to_geo_boundary(UInt64) :: Array(Tuple(Float64, Float64))
1 h3_to_geo_boundary(UInt64 NULL) :: Array(Tuple(Float64, Float64)) NULL
0 h3_to_parent(UInt64, UInt8) :: UInt64
1 h3_to_parent(UInt64 NULL, UInt8 NULL) :: UInt64 NULL
0 h3_to_string(UInt64) :: String
1 h3_to_string(UInt64 NULL) :: String NULL
0 h3_unidirectional_edge_is_valid(UInt64) :: Boolean
1 h3_unidirectional_edge_is_valid(UInt64 NULL) :: Boolean NULL
0 haversine(Float64, Float64, Float64, Float64) :: Float64
1 haversine(Float64 NULL, Float64 NULL, Float64 NULL, Float64 NULL) :: Float64 NULL
0 hilbert_range_index FACTORY
0 humanize_number(Float64) :: String
1 humanize_number(Float64 NULL) :: String NULL
0 humanize_size(Float64) :: String
1 humanize_size(Float64 NULL) :: String NULL
0 if FACTORY
0 ignore FACTORY
0 inet_aton(String) :: UInt32
1 inet_aton(String NULL) :: UInt32 NULL
0 inet_ntoa(Int64) :: String
1 inet_ntoa(Int64 NULL) :: String NULL
0 insert(String, Int64, Int64, String) :: String
1 insert(String NULL, Int64 NULL, Int64 NULL, String NULL) :: String NULL
0 instr(String, String) :: UInt64
1 instr(String NULL, String NULL) :: UInt64 NULL
2 instr(String, String, Int64) :: UInt64
3 instr(String NULL, String NULL, Int64 NULL) :: UInt64 NULL
4 instr(String, String, Int64, UInt64) :: UInt64
5 instr(String NULL, String NULL, Int64 NULL, UInt64 NULL) :: UInt64 NULL
0 is_array(Variant) :: Boolean
1 is_array(Variant NULL) :: Boolean NULL
0 is_binary(Variant) :: Boolean
1 is_binary(Variant NULL) :: Boolean NULL
0 is_boolean(Variant) :: Boolean
1 is_boolean(Variant NULL) :: Boolean NULL
0 is_date(Variant) :: Boolean
1 is_date(Variant NULL) :: Boolean NULL
0 is_float(Variant) :: Boolean
1 is_float(Variant NULL) :: Boolean NULL
0 is_integer(Variant) :: Boolean
1 is_integer(Variant NULL) :: Boolean NULL
0 is_interval(Variant) :: Boolean
1 is_interval(Variant NULL) :: Boolean NULL
0 is_not_error(T0) :: Boolean
0 is_not_null(NULL) :: Boolean
1 is_not_null(T0 NULL) :: Boolean
0 is_null_value(Variant) :: Boolean
1 is_null_value(Variant NULL) :: Boolean NULL
0 is_object(Variant) :: Boolean
1 is_object(Variant NULL) :: Boolean NULL
0 is_string(Variant) :: Boolean
1 is_string(Variant NULL) :: Boolean NULL
0 is_timestamp(Variant) :: Boolean
1 is_timestamp(Variant NULL) :: Boolean NULL
0 is_true(Boolean) :: Boolean
1 is_true(Boolean NULL) :: Boolean
0 jaro_winkler(String, String) :: Float64
1 jaro_winkler(String NULL, String NULL) :: Float64 NULL
0 jq FACTORY
0 json_array FACTORY
0 json_array_distinct(Variant) :: Variant
1 json_array_distinct(Variant NULL) :: Variant NULL
0 json_array_elements FACTORY
0 json_array_except(Variant, Variant) :: Variant
1 json_array_except(Variant NULL, Variant NULL) :: Variant NULL
0 json_array_insert(Variant, Int32, Variant) :: Variant
1 json_array_insert(Variant NULL, Int32 NULL, Variant NULL) :: Variant NULL
0 json_array_intersection(Variant, Variant) :: Variant
1 json_array_intersection(Variant NULL, Variant NULL) :: Variant NULL
0 json_array_overlap(Variant, Variant) :: Boolean
1 json_array_overlap(Variant NULL, Variant NULL) :: Boolean NULL
0 json_contains_in_left(Variant, Variant) :: Boolean
1 json_contains_in_left(Variant NULL, Variant NULL) :: Boolean NULL
0 json_contains_in_right(Variant, Variant) :: Boolean
1 json_contains_in_right(Variant NULL, Variant NULL) :: Boolean NULL
0 json_each FACTORY
0 json_exists_all_keys(Variant, Array(String)) :: Boolean
1 json_exists_all_keys(Variant NULL, Array(String) NULL) :: Boolean NULL
0 json_exists_any_keys(Variant, Array(String)) :: Boolean
1 json_exists_any_keys(Variant NULL, Array(String) NULL) :: Boolean NULL
0 json_exists_key(Variant, String) :: Boolean
1 json_exists_key(Variant NULL, String NULL) :: Boolean NULL
0 json_extract_path_text(String, String) :: String NULL
1 json_extract_path_text(String NULL, String NULL) :: String NULL
0 json_object FACTORY
0 json_object_delete FACTORY
0 json_object_insert FACTORY
0 json_object_keep_null FACTORY
0 json_object_keys(Variant NULL) :: Variant NULL
0 json_object_pick FACTORY
0 json_path_exists FACTORY
0 json_path_match FACTORY
0 json_path_query FACTORY
0 json_path_query_array(Variant, String) :: Variant NULL
1 json_path_query_array(Variant NULL, String NULL) :: Variant NULL
0 json_path_query_first(Variant, String) :: Variant NULL
1 json_path_query_first(Variant NULL, String NULL) :: Variant NULL
0 json_pretty(Variant) :: String
1 json_pretty(Variant NULL) :: String NULL
0 json_strip_nulls(Variant) :: Variant
1 json_strip_nulls(Variant NULL) :: Variant NULL
0 json_typeof(Variant) :: String
1 json_typeof(Variant NULL) :: String NULL
0 l2_distance(Array(Float32), Array(Float32)) :: Float32
1 l2_distance(Array(Float32) NULL, Array(Float32) NULL) :: Float32 NULL
2 l2_distance(Array(Float64), Array(Float64)) :: Float64
3 l2_distance(Array(Float64) NULL, Array(Float64) NULL) :: Float64 NULL
0 left(String, UInt64) :: String
1 left(String NULL, UInt64 NULL) :: String NULL
0 length(Variant NULL) :: UInt32 NULL
1 length(Array(Nothing)) :: UInt8
2 length(Array(Nothing) NULL) :: UInt8 NULL
3 length(Array(T0)) :: UInt64
4 length(Array(T0) NULL) :: UInt64 NULL
5 length(String) :: UInt64
6 length(String NULL) :: UInt64 NULL
7 length(Binary) :: UInt64
8 length(Binary NULL) :: UInt64 NULL
0 like(Variant, String) :: Boolean
1 like(Variant NULL, String NULL) :: Boolean NULL
2 like(Variant, String, String) :: Boolean
3 like(Variant NULL, String NULL, String NULL) :: Boolean NULL
4 like(String, String) :: Boolean
5 like(String NULL, String NULL) :: Boolean NULL
6 like(String, String, String) :: Boolean
7 like(String NULL, String NULL, String NULL) :: Boolean NULL
0 like_any FACTORY
0 ln(UInt8) :: Float64
1 ln(UInt8 NULL) :: Float64 NULL
2 ln(UInt16) :: Float64
3 ln(UInt16 NULL) :: Float64 NULL
4 ln(UInt32) :: Float64
5 ln(UInt32 NULL) :: Float64 NULL
6 ln(UInt64) :: Float64
7 ln(UInt64 NULL) :: Float64 NULL
8 ln(Int8) :: Float64
9 ln(Int8 NULL) :: Float64 NULL
10 ln(Int16) :: Float64
11 ln(Int16 NULL) :: Float64 NULL
12 ln(Int32) :: Float64
13 ln(Int32 NULL) :: Float64 NULL
14 ln(Int64) :: Float64
15 ln(Int64 NULL) :: Float64 NULL
16 ln(Float32) :: Float64
17 ln(Float32 NULL) :: Float64 NULL
18 ln(Float64) :: Float64
19 ln(Float64 NULL) :: Float64 NULL
0 locate(String, String) :: UInt64
1 locate(String NULL, String NULL) :: UInt64 NULL
2 locate(String, String, UInt64) :: UInt64
3 locate(String NULL, String NULL, UInt64 NULL) :: UInt64 NULL
0 log(UInt8) :: Float64
1 log(UInt8 NULL) :: Float64 NULL
2 log(UInt8, Float64) :: Float64
3 log(UInt8 NULL, Float64 NULL) :: Float64 NULL
4 log(UInt16) :: Float64
5 log(UInt16 NULL) :: Float64 NULL
6 log(UInt16, Float64) :: Float64
7 log(UInt16 NULL, Float64 NULL) :: Float64 NULL
8 log(UInt32) :: Float64
9 log(UInt32 NULL) :: Float64 NULL
10 log(UInt32, Float64) :: Float64
11 log(UInt32 NULL, Float64 NULL) :: Float64 NULL
12 log(UInt64) :: Float64
13 log(UInt64 NULL) :: Float64 NULL
14 log(UInt64, Float64) :: Float64
15 log(UInt64 NULL, Float64 NULL) :: Float64 NULL
16 log(Int8) :: Float64
17 log(Int8 NULL) :: Float64 NULL
18 log(Int8, Float64) :: Float64
19 log(Int8 NULL, Float64 NULL) :: Float64 NULL
20 log(Int16) :: Float64
21 log(Int16 NULL) :: Float64 NULL
22 log(Int16, Float64) :: Float64
23 log(Int16 NULL, Float64 NULL) :: Float64 NULL
24 log(Int32) :: Float64
25 log(Int32 NULL) :: Float64 NULL
26 log(Int32, Float64) :: Float64
27 log(Int32 NULL, Float64 NULL) :: Float64 NULL
28 log(Int64) :: Float64
29 log(Int64 NULL) :: Float64 NULL
30 log(Int64, Float64) :: Float64
31 log(Int64 NULL, Float64 NULL) :: Float64 NULL
32 log(Float32) :: Float64
33 log(Float32 NULL) :: Float64 NULL
34 log(Float32, Float64) :: Float64
35 log(Float32 NULL, Float64 NULL) :: Float64 NULL
36 log(Float64) :: Float64
37 log(Float64 NULL) :: Float64 NULL
38 log(Float64, Float64) :: Float64
39 log(Float64 NULL, Float64 NULL) :: Float64 NULL
0 log10(UInt8) :: Float64
1 log10(UInt8 NULL) :: Float64 NULL
2 log10(UInt16) :: Float64
3 log10(UInt16 NULL) :: Float64 NULL
4 log10(UInt32) :: Float64
5 log10(UInt32 NULL) :: Float64 NULL
6 log10(UInt64) :: Float64
7 log10(UInt64 NULL) :: Float64 NULL
8 log10(Int8) :: Float64
9 log10(Int8 NULL) :: Float64 NULL
10 log10(Int16) :: Float64
11 log10(Int16 NULL) :: Float64 NULL
12 log10(Int32) :: Float64
13 log10(Int32 NULL) :: Float64 NULL
14 log10(Int64) :: Float64
15 log10(Int64 NULL) :: Float64 NULL
16 log10(Float32) :: Float64
17 log10(Float32 NULL) :: Float64 NULL
18 log10(Float64) :: Float64
19 log10(Float64 NULL) :: Float64 NULL
0 log2(UInt8) :: Float64
1 log2(UInt8 NULL) :: Float64 NULL
2 log2(UInt16) :: Float64
3 log2(UInt16 NULL) :: Float64 NULL
4 log2(UInt32) :: Float64
5 log2(UInt32 NULL) :: Float64 NULL
6 log2(UInt64) :: Float64
7 log2(UInt64 NULL) :: Float64 NULL
8 log2(Int8) :: Float64
9 log2(Int8 NULL) :: Float64 NULL
10 log2(Int16) :: Float64
11 log2(Int16 NULL) :: Float64 NULL
12 log2(Int32) :: Float64
13 log2(Int32 NULL) :: Float64 NULL
14 log2(Int64) :: Float64
15 log2(Int64 NULL) :: Float64 NULL
16 log2(Float32) :: Float64
17 log2(Float32 NULL) :: Float64 NULL
18 log2(Float64) :: Float64
19 log2(Float64 NULL) :: Float64 NULL
0 lower(String) :: String
1 lower(String NULL) :: String NULL
0 lpad(String, UInt64, String) :: String
1 lpad(String NULL, UInt64 NULL, String NULL) :: String NULL
0 lt(Variant, Variant) :: Boolean
1 lt(Variant NULL, Variant NULL) :: Boolean NULL
2 lt(String, String) :: Boolean
3 lt(String NULL, String NULL) :: Boolean NULL
4 lt(Date, Date) :: Boolean
5 lt(Date NULL, Date NULL) :: Boolean NULL
6 lt(Timestamp, Timestamp) :: Boolean
7 lt(Timestamp NULL, Timestamp NULL) :: Boolean NULL
8 lt(UInt8, UInt8) :: Boolean
9 lt(UInt8 NULL, UInt8 NULL) :: Boolean NULL
10 lt(Int8, Int8) :: Boolean
11 lt(Int8 NULL, Int8 NULL) :: Boolean NULL
12 lt(UInt16, UInt16) :: Boolean
13 lt(UInt16 NULL, UInt16 NULL) :: Boolean NULL
14 lt(Int16, Int16) :: Boolean
15 lt(Int16 NULL, Int16 NULL) :: Boolean NULL
16 lt(UInt32, UInt32) :: Boolean
17 lt(UInt32 NULL, UInt32 NULL) :: Boolean NULL
18 lt(Int32, Int32) :: Boolean
19 lt(Int32 NULL, Int32 NULL) :: Boolean NULL
20 lt(UInt64, UInt64) :: Boolean
21 lt(UInt64 NULL, UInt64 NULL) :: Boolean NULL
22 lt(Int64, Int64) :: Boolean
23 lt(Int64 NULL, Int64 NULL) :: Boolean NULL
24 lt FACTORY
25 lt(Float32, Float32) :: Boolean
26 lt(Float32 NULL, Float32 NULL) :: Boolean NULL
27 lt(Float64, Float64) :: Boolean
28 lt(Float64 NULL, Float64 NULL) :: Boolean NULL
29 lt(String, UInt8) :: Boolean
30 lt(String NULL, UInt8 NULL) :: Boolean NULL
31 lt(UInt8, String) :: Boolean
32 lt(UInt8 NULL, String NULL) :: Boolean NULL
33 lt(String, UInt16) :: Boolean
34 lt(String NULL, UInt16 NULL) :: Boolean NULL
35 lt(UInt16, String) :: Boolean
36 lt(UInt16 NULL, String NULL) :: Boolean NULL
37 lt(String, UInt32) :: Boolean
38 lt(String NULL, UInt32 NULL) :: Boolean NULL
39 lt(UInt32, String) :: Boolean
40 lt(UInt32 NULL, String NULL) :: Boolean NULL
41 lt(String, UInt64) :: Boolean
42 lt(String NULL, UInt64 NULL) :: Boolean NULL
43 lt(UInt64, String) :: Boolean
44 lt(UInt64 NULL, String NULL) :: Boolean NULL
45 lt(String, Int8) :: Boolean
46 lt(String NULL, Int8 NULL) :: Boolean NULL
47 lt(Int8, String) :: Boolean
48 lt(Int8 NULL, String NULL) :: Boolean NULL
49 lt(String, Int16) :: Boolean
50 lt(String NULL, Int16 NULL) :: Boolean NULL
51 lt(Int16, String) :: Boolean
52 lt(Int16 NULL, String NULL) :: Boolean NULL
53 lt(String, Int32) :: Boolean
54 lt(String NULL, Int32 NULL) :: Boolean NULL
55 lt(Int32, String) :: Boolean
56 lt(Int32 NULL, String NULL) :: Boolean NULL
57 lt(String, Int64) :: Boolean
58 lt(String NULL, Int64 NULL) :: Boolean NULL
59 lt(Int64, String) :: Boolean
60 lt(Int64 NULL, String NULL) :: Boolean NULL
61 lt(String, Float32) :: Boolean
62 lt(String NULL, Float32 NULL) :: Boolean NULL
63 lt(Float32, String) :: Boolean
64 lt(Float32 NULL, String NULL) :: Boolean NULL
65 lt(String, Float64) :: Boolean
66 lt(String NULL, Float64 NULL) :: Boolean NULL
67 lt(Float64, String) :: Boolean
68 lt(Float64 NULL, String NULL) :: Boolean NULL
69 lt(Boolean, Boolean) :: Boolean
70 lt(Boolean NULL, Boolean NULL) :: Boolean NULL
71 lt(Array(Nothing), Array(Nothing)) :: Boolean
72 lt(Array(Nothing) NULL, Array(Nothing) NULL) :: Boolean NULL
73 lt(Array(T0), Array(T0)) :: Boolean
74 lt(Array(T0) NULL, Array(T0) NULL) :: Boolean NULL
75 lt FACTORY
76 lt(Interval, Interval) :: Boolean
77 lt(Interval NULL, Interval NULL) :: Boolean NULL
0 lte(Variant, Variant) :: Boolean
1 lte(Variant NULL, Variant NULL) :: Boolean NULL
2 lte(String, String) :: Boolean
3 lte(String NULL, String NULL) :: Boolean NULL
4 lte(Date, Date) :: Boolean
5 lte(Date NULL, Date NULL) :: Boolean NULL
6 lte(Timestamp, Timestamp) :: Boolean
7 lte(Timestamp NULL, Timestamp NULL) :: Boolean NULL
8 lte(UInt8, UInt8) :: Boolean
9 lte(UInt8 NULL, UInt8 NULL) :: Boolean NULL
10 lte(Int8, Int8) :: Boolean
11 lte(Int8 NULL, Int8 NULL) :: Boolean NULL
12 lte(UInt16, UInt16) :: Boolean
13 lte(UInt16 NULL, UInt16 NULL) :: Boolean NULL
14 lte(Int16, Int16) :: Boolean
15 lte(Int16 NULL, Int16 NULL) :: Boolean NULL
16 lte(UInt32, UInt32) :: Boolean
17 lte(UInt32 NULL, UInt32 NULL) :: Boolean NULL
18 lte(Int32, Int32) :: Boolean
19 lte(Int32 NULL, Int32 NULL) :: Boolean NULL
20 lte(UInt64, UInt64) :: Boolean
21 lte(UInt64 NULL, UInt64 NULL) :: Boolean NULL
22 lte(Int64, Int64) :: Boolean
23 lte(Int64 NULL, Int64 NULL) :: Boolean NULL
24 lte FACTORY
25 lte(Float32, Float32) :: Boolean
26 lte(Float32 NULL, Float32 NULL) :: Boolean NULL
27 lte(Float64, Float64) :: Boolean
28 lte(Float64 NULL, Float64 NULL) :: Boolean NULL
29 lte(String, UInt8) :: Boolean
30 lte(String NULL, UInt8 NULL) :: Boolean NULL
31 lte(UInt8, String) :: Boolean
32 lte(UInt8 NULL, String NULL) :: Boolean NULL
33 lte(String, UInt16) :: Boolean
34 lte(String NULL, UInt16 NULL) :: Boolean NULL
35 lte(UInt16, String) :: Boolean
36 lte(UInt16 NULL, String NULL) :: Boolean NULL
37 lte(String, UInt32) :: Boolean
38 lte(String NULL, UInt32 NULL) :: Boolean NULL
39 lte(UInt32, String) :: Boolean
40 lte(UInt32 NULL, String NULL) :: Boolean NULL
41 lte(String, UInt64) :: Boolean
42 lte(String NULL, UInt64 NULL) :: Boolean NULL
43 lte(UInt64, String) :: Boolean
44 lte(UInt64 NULL, String NULL) :: Boolean NULL
45 lte(String, Int8) :: Boolean
46 lte(String NULL, Int8 NULL) :: Boolean NULL
47 lte(Int8, String) :: Boolean
48 lte(Int8 NULL, String NULL) :: Boolean NULL
49 lte(String, Int16) :: Boolean
50 lte(String NULL, Int16 NULL) :: Boolean NULL
51 lte(Int16, String) :: Boolean
52 lte(Int16 NULL, String NULL) :: Boolean NULL
53 lte(String, Int32) :: Boolean
54 lte(String NULL, Int32 NULL) :: Boolean NULL
55 lte(Int32, String) :: Boolean
56 lte(Int32 NULL, String NULL) :: Boolean NULL
57 lte(String, Int64) :: Boolean
58 lte(String NULL, Int64 NULL) :: Boolean NULL
59 lte(Int64, String) :: Boolean
60 lte(Int64 NULL, String NULL) :: Boolean NULL
61 lte(String, Float32) :: Boolean
62 lte(String NULL, Float32 NULL) :: Boolean NULL
63 lte(Float32, String) :: Boolean
64 lte(Float32 NULL, String NULL) :: Boolean NULL
65 lte(String, Float64) :: Boolean
66 lte(String NULL, Float64 NULL) :: Boolean NULL
67 lte(Float64, String) :: Boolean
68 lte(Float64 NULL, String NULL) :: Boolean NULL
69 lte(Boolean, Boolean) :: Boolean
70 lte(Boolean NULL, Boolean NULL) :: Boolean NULL
71 lte(Array(Nothing), Array(Nothing)) :: Boolean
72 lte(Array(Nothing) NULL, Array(Nothing) NULL) :: Boolean NULL
73 lte(Array(T0), Array(T0)) :: Boolean
74 lte(Array(T0) NULL, Array(T0) NULL) :: Boolean NULL
75 lte FACTORY
76 lte(Interval, Interval) :: Boolean
77 lte(Interval NULL, Interval NULL) :: Boolean NULL
0 ltrim(String) :: String
1 ltrim(String NULL) :: String NULL
2 ltrim(String, String) :: String
3 ltrim(String NULL, String NULL) :: String NULL
0 map(Array(Nothing), Array(Nothing)) :: Map(Nothing)
1 map(Array(Nothing) NULL, Array(Nothing) NULL) :: Map(Nothing) NULL
2 map(Array(T0), Array(T1)) :: Map(T0, T1)
3 map(Array(T0) NULL, Array(T1) NULL) :: Map(T0, T1) NULL
0 map_cat(Map(Nothing), Map(Nothing)) :: Map(Nothing)
1 map_cat(Map(Nothing) NULL, Map(Nothing) NULL) :: Map(Nothing) NULL
2 map_cat(Map(T0, T1), Map(T0, T1)) :: Map(T0, T1)
3 map_cat(Map(T0, T1) NULL, Map(T0, T1) NULL) :: Map(T0, T1) NULL
0 map_contains_key(Map(Nothing), T0) :: Boolean
1 map_contains_key(Map(T0, T1), T0) :: Boolean
2 map_contains_key(Map(T0, T1) NULL, T0 NULL) :: Boolean NULL
0 map_delete FACTORY
0 map_insert(Map(T0, T1) NULL, T0, T1) :: Map(T0, T1)
1 map_insert(Map(T0, T1) NULL, T0, T1, Boolean) :: Map(T0, T1)
0 map_keys(Map(Nothing)) :: Array(Nothing)
1 map_keys(Map(T0, T1)) :: Array(T0)
2 map_keys(Map(T0, T1) NULL) :: Array(T0) NULL
0 map_pick FACTORY
0 map_size(Map(Nothing)) :: UInt8
1 map_size(Map(T0, T1)) :: UInt64
2 map_size(Map(T0, T1) NULL) :: UInt64 NULL
0 map_values(Map(Nothing)) :: Array(Nothing)
1 map_values(Map(T0, T1)) :: Array(T1)
2 map_values(Map(T0, T1) NULL) :: Array(T1) NULL
0 markov_generate(Array(T0), String, UInt64, String) :: String
1 markov_generate(Array(T0) NULL, String NULL, UInt64 NULL, String NULL) :: String NULL
0 md5(String) :: String
1 md5(String NULL) :: String NULL
0 millennium(Date) :: UInt16
1 millennium(Date NULL) :: UInt16 NULL
2 millennium(Timestamp) :: UInt16
3 millennium(Timestamp NULL) :: UInt16 NULL
0 minus(Variant, Int32) :: Variant
1 minus(Variant NULL, Int32 NULL) :: Variant NULL
2 minus(Variant, String) :: Variant
3 minus(Variant NULL, String NULL) :: Variant NULL
4 minus FACTORY
5 minus(UInt8) :: Int16
6 minus(UInt8 NULL) :: Int16 NULL
7 minus(Int8) :: Int16
8 minus(Int8 NULL) :: Int16 NULL
9 minus(UInt16) :: Int32
10 minus(UInt16 NULL) :: Int32 NULL
11 minus(Int16) :: Int32
12 minus(Int16 NULL) :: Int32 NULL
13 minus(UInt32) :: Int64
14 minus(UInt32 NULL) :: Int64 NULL
15 minus(Int32) :: Int64
16 minus(Int32 NULL) :: Int64 NULL
17 minus(UInt64) :: Int64
18 minus(UInt64 NULL) :: Int64 NULL
19 minus(Int64) :: Int64
20 minus(Int64 NULL) :: Int64 NULL
21 minus(Float32) :: Float32
22 minus(Float32 NULL) :: Float32 NULL
23 minus(Float64) :: Float64
24 minus(Float64 NULL) :: Float64 NULL
25 minus FACTORY
26 minus(UInt8, UInt8) :: Int16
27 minus(UInt8 NULL, UInt8 NULL) :: Int16 NULL
28 minus(UInt8, UInt16) :: Int32
29 minus(UInt8 NULL, UInt16 NULL) :: Int32 NULL
30 minus(UInt8, UInt32) :: Int64
31 minus(UInt8 NULL, UInt32 NULL) :: Int64 NULL
32 minus(UInt8, UInt64) :: Int64
33 minus(UInt8 NULL, UInt64 NULL) :: Int64 NULL
34 minus(UInt8, Int8) :: Int16
35 minus(UInt8 NULL, Int8 NULL) :: Int16 NULL
36 minus(UInt8, Int16) :: Int32
37 minus(UInt8 NULL, Int16 NULL) :: Int32 NULL
38 minus(UInt8, Int32) :: Int64
39 minus(UInt8 NULL, Int32 NULL) :: Int64 NULL
40 minus(UInt8, Int64) :: Int64
41 minus(UInt8 NULL, Int64 NULL) :: Int64 NULL
42 minus(UInt16, UInt8) :: Int32
43 minus(UInt16 NULL, UInt8 NULL) :: Int32 NULL
44 minus(UInt16, UInt16) :: Int32
45 minus(UInt16 NULL, UInt16 NULL) :: Int32 NULL
46 minus(UInt16, UInt32) :: Int64
47 minus(UInt16 NULL, UInt32 NULL) :: Int64 NULL
48 minus(UInt16, UInt64) :: Int64
49 minus(UInt16 NULL, UInt64 NULL) :: Int64 NULL
50 minus(UInt16, Int8) :: Int32
51 minus(UInt16 NULL, Int8 NULL) :: Int32 NULL
52 minus(UInt16, Int16) :: Int32
53 minus(UInt16 NULL, Int16 NULL) :: Int32 NULL
54 minus(UInt16, Int32) :: Int64
55 minus(UInt16 NULL, Int32 NULL) :: Int64 NULL
56 minus(UInt16, Int64) :: Int64
57 minus(UInt16 NULL, Int64 NULL) :: Int64 NULL
58 minus(UInt32, UInt8) :: Int64
59 minus(UInt32 NULL, UInt8 NULL) :: Int64 NULL
60 minus(UInt32, UInt16) :: Int64
61 minus(UInt32 NULL, UInt16 NULL) :: Int64 NULL
62 minus(UInt32, UInt32) :: Int64
63 minus(UInt32 NULL, UInt32 NULL) :: Int64 NULL
64 minus(UInt32, UInt64) :: Int64
65 minus(UInt32 NULL, UInt64 NULL) :: Int64 NULL
66 minus(UInt32, Int8) :: Int64
67 minus(UInt32 NULL, Int8 NULL) :: Int64 NULL
68 minus(UInt32, Int16) :: Int64
69 minus(UInt32 NULL, Int16 NULL) :: Int64 NULL
70 minus(UInt32, Int32) :: Int64
71 minus(UInt32 NULL, Int32 NULL) :: Int64 NULL
72 minus(UInt32, Int64) :: Int64
73 minus(UInt32 NULL, Int64 NULL) :: Int64 NULL
74 minus(UInt64, UInt8) :: Int64
75 minus(UInt64 NULL, UInt8 NULL) :: Int64 NULL
76 minus(UInt64, UInt16) :: Int64
77 minus(UInt64 NULL, UInt16 NULL) :: Int64 NULL
78 minus(UInt64, UInt32) :: Int64
79 minus(UInt64 NULL, UInt32 NULL) :: Int64 NULL
80 minus(UInt64, UInt64) :: Int64
81 minus(UInt64 NULL, UInt64 NULL) :: Int64 NULL
82 minus(UInt64, Int8) :: Int64
83 minus(UInt64 NULL, Int8 NULL) :: Int64 NULL
84 minus(UInt64, Int16) :: Int64
85 minus(UInt64 NULL, Int16 NULL) :: Int64 NULL
86 minus(UInt64, Int32) :: Int64
87 minus(UInt64 NULL, Int32 NULL) :: Int64 NULL
88 minus(UInt64, Int64) :: Int64
89 minus(UInt64 NULL, Int64 NULL) :: Int64 NULL
90 minus(Int8, UInt8) :: Int16
91 minus(Int8 NULL, UInt8 NULL) :: Int16 NULL
92 minus(Int8, UInt16) :: Int32
93 minus(Int8 NULL, UInt16 NULL) :: Int32 NULL
94 minus(Int8, UInt32) :: Int64
95 minus(Int8 NULL, UInt32 NULL) :: Int64 NULL
96 minus(Int8, UInt64) :: Int64
97 minus(Int8 NULL, UInt64 NULL) :: Int64 NULL
98 minus(Int8, Int8) :: Int16
99 minus(Int8 NULL, Int8 NULL) :: Int16 NULL
100 minus(Int8, Int16) :: Int32
101 minus(Int8 NULL, Int16 NULL) :: Int32 NULL
102 minus(Int8, Int32) :: Int64
103 minus(Int8 NULL, Int32 NULL) :: Int64 NULL
104 minus(Int8, Int64) :: Int64
105 minus(Int8 NULL, Int64 NULL) :: Int64 NULL
106 minus(Int16, UInt8) :: Int32
107 minus(Int16 NULL, UInt8 NULL) :: Int32 NULL
108 minus(Int16, UInt16) :: Int32
109 minus(Int16 NULL, UInt16 NULL) :: Int32 NULL
110 minus(Int16, UInt32) :: Int64
111 minus(Int16 NULL, UInt32 NULL) :: Int64 NULL
112 minus(Int16, UInt64) :: Int64
113 minus(Int16 NULL, UInt64 NULL) :: Int64 NULL
114 minus(Int16, Int8) :: Int32
115 minus(Int16 NULL, Int8 NULL) :: Int32 NULL
116 minus(Int16, Int16) :: Int32
117 minus(Int16 NULL, Int16 NULL) :: Int32 NULL
118 minus(Int16, Int32) :: Int64
119 minus(Int16 NULL, Int32 NULL) :: Int64 NULL
120 minus(Int16, Int64) :: Int64
121 minus(Int16 NULL, Int64 NULL) :: Int64 NULL
122 minus(Int32, UInt8) :: Int64
123 minus(Int32 NULL, UInt8 NULL) :: Int64 NULL
124 minus(Int32, UInt16) :: Int64
125 minus(Int32 NULL, UInt16 NULL) :: Int64 NULL
126 minus(Int32, UInt32) :: Int64
127 minus(Int32 NULL, UInt32 NULL) :: Int64 NULL
128 minus(Int32, UInt64) :: Int64
129 minus(Int32 NULL, UInt64 NULL) :: Int64 NULL
130 minus(Int32, Int8) :: Int64
131 minus(Int32 NULL, Int8 NULL) :: Int64 NULL
132 minus(Int32, Int16) :: Int64
133 minus(Int32 NULL, Int16 NULL) :: Int64 NULL
134 minus(Int32, Int32) :: Int64
135 minus(Int32 NULL, Int32 NULL) :: Int64 NULL
136 minus(Int32, Int64) :: Int64
137 minus(Int32 NULL, Int64 NULL) :: Int64 NULL
138 minus(Int64, UInt8) :: Int64
139 minus(Int64 NULL, UInt8 NULL) :: Int64 NULL
140 minus(Int64, UInt16) :: Int64
141 minus(Int64 NULL, UInt16 NULL) :: Int64 NULL
142 minus(Int64, UInt32) :: Int64
143 minus(Int64 NULL, UInt32 NULL) :: Int64 NULL
144 minus(Int64, UInt64) :: Int64
145 minus(Int64 NULL, UInt64 NULL) :: Int64 NULL
146 minus(Int64, Int8) :: Int64
147 minus(Int64 NULL, Int8 NULL) :: Int64 NULL
148 minus(Int64, Int16) :: Int64
149 minus(Int64 NULL, Int16 NULL) :: Int64 NULL
150 minus(Int64, Int32) :: Int64
151 minus(Int64 NULL, Int32 NULL) :: Int64 NULL
152 minus(Int64, Int64) :: Int64
153 minus(Int64 NULL, Int64 NULL) :: Int64 NULL
154 minus(UInt8, Float32) :: Float64
155 minus(UInt8 NULL, Float32 NULL) :: Float64 NULL
156 minus(UInt8, Float64) :: Float64
157 minus(UInt8 NULL, Float64 NULL) :: Float64 NULL
158 minus(UInt16, Float32) :: Float64
159 minus(UInt16 NULL, Float32 NULL) :: Float64 NULL
160 minus(UInt16, Float64) :: Float64
161 minus(UInt16 NULL, Float64 NULL) :: Float64 NULL
162 minus(UInt32, Float32) :: Float64
163 minus(UInt32 NULL, Float32 NULL) :: Float64 NULL
164 minus(UInt32, Float64) :: Float64
165 minus(UInt32 NULL, Float64 NULL) :: Float64 NULL
166 minus(UInt64, Float32) :: Float64
167 minus(UInt64 NULL, Float32 NULL) :: Float64 NULL
168 minus(UInt64, Float64) :: Float64
169 minus(UInt64 NULL, Float64 NULL) :: Float64 NULL
170 minus(Int8, Float32) :: Float64
171 minus(Int8 NULL, Float32 NULL) :: Float64 NULL
172 minus(Int8, Float64) :: Float64
173 minus(Int8 NULL, Float64 NULL) :: Float64 NULL
174 minus(Int16, Float32) :: Float64
175 minus(Int16 NULL, Float32 NULL) :: Float64 NULL
176 minus(Int16, Float64) :: Float64
177 minus(Int16 NULL, Float64 NULL) :: Float64 NULL
178 minus(Int32, Float32) :: Float64
179 minus(Int32 NULL, Float32 NULL) :: Float64 NULL
180 minus(Int32, Float64) :: Float64
181 minus(Int32 NULL, Float64 NULL) :: Float64 NULL
182 minus(Int64, Float32) :: Float64
183 minus(Int64 NULL, Float32 NULL) :: Float64 NULL
184 minus(Int64, Float64) :: Float64
185 minus(Int64 NULL, Float64 NULL) :: Float64 NULL
186 minus(Float32, UInt8) :: Float64
187 minus(Float32 NULL, UInt8 NULL) :: Float64 NULL
188 minus(Float32, UInt16) :: Float64
189 minus(Float32 NULL, UInt16 NULL) :: Float64 NULL
190 minus(Float32, UInt32) :: Float64
191 minus(Float32 NULL, UInt32 NULL) :: Float64 NULL
192 minus(Float32, UInt64) :: Float64
193 minus(Float32 NULL, UInt64 NULL) :: Float64 NULL
194 minus(Float32, Int8) :: Float64
195 minus(Float32 NULL, Int8 NULL) :: Float64 NULL
196 minus(Float32, Int16) :: Float64
197 minus(Float32 NULL, Int16 NULL) :: Float64 NULL
198 minus(Float32, Int32) :: Float64
199 minus(Float32 NULL, Int32 NULL) :: Float64 NULL
200 minus(Float32, Int64) :: Float64
201 minus(Float32 NULL, Int64 NULL) :: Float64 NULL
202 minus(Float64, UInt8) :: Float64
203 minus(Float64 NULL, UInt8 NULL) :: Float64 NULL
204 minus(Float64, UInt16) :: Float64
205 minus(Float64 NULL, UInt16 NULL) :: Float64 NULL
206 minus(Float64, UInt32) :: Float64
207 minus(Float64 NULL, UInt32 NULL) :: Float64 NULL
208 minus(Float64, UInt64) :: Float64
209 minus(Float64 NULL, UInt64 NULL) :: Float64 NULL
210 minus(Float64, Int8) :: Float64
211 minus(Float64 NULL, Int8 NULL) :: Float64 NULL
212 minus(Float64, Int16) :: Float64
213 minus(Float64 NULL, Int16 NULL) :: Float64 NULL
214 minus(Float64, Int32) :: Float64
215 minus(Float64 NULL, Int32 NULL) :: Float64 NULL
216 minus(Float64, Int64) :: Float64
217 minus(Float64 NULL, Int64 NULL) :: Float64 NULL
218 minus(Float32, Float32) :: Float64
219 minus(Float32 NULL, Float32 NULL) :: Float64 NULL
220 minus(Float32, Float64) :: Float64
221 minus(Float32 NULL, Float64 NULL) :: Float64 NULL
222 minus(Float64, Float32) :: Float64
223 minus(Float64 NULL, Float32 NULL) :: Float64 NULL
224 minus(Float64, Float64) :: Float64
225 minus(Float64 NULL, Float64 NULL) :: Float64 NULL
226 minus(Date, Date) :: Int32
227 minus(Date NULL, Date NULL) :: Int32 NULL
228 minus(Timestamp, Timestamp) :: Int64
229 minus(Timestamp NULL, Timestamp NULL) :: Int64 NULL
230 minus(Date, Int64) :: Date
231 minus(Date NULL, Int64 NULL) :: Date NULL
232 minus(Timestamp, Int64) :: Timestamp
233 minus(Timestamp NULL, Int64 NULL) :: Timestamp NULL
234 minus(Interval, Interval) :: Interval
235 minus(Interval NULL, Interval NULL) :: Interval NULL
236 minus(Timestamp, Interval) :: Timestamp
237 minus(Timestamp NULL, Interval NULL) :: Timestamp NULL
0 modulo(UInt8, UInt8) :: UInt8
1 modulo(UInt8 NULL, UInt8 NULL) :: UInt8 NULL
2 modulo(UInt8, UInt16) :: UInt16
3 modulo(UInt8 NULL, UInt16 NULL) :: UInt16 NULL
4 modulo(UInt8, UInt32) :: UInt32
5 modulo(UInt8 NULL, UInt32 NULL) :: UInt32 NULL
6 modulo(UInt8, UInt64) :: UInt64
7 modulo(UInt8 NULL, UInt64 NULL) :: UInt64 NULL
8 modulo(UInt8, Int8) :: UInt8
9 modulo(UInt8 NULL, Int8 NULL) :: UInt8 NULL
10 modulo(UInt8, Int16) :: UInt16
11 modulo(UInt8 NULL, Int16 NULL) :: UInt16 NULL
12 modulo(UInt8, Int32) :: UInt32
13 modulo(UInt8 NULL, Int32 NULL) :: UInt32 NULL
14 modulo(UInt8, Int64) :: UInt64
15 modulo(UInt8 NULL, Int64 NULL) :: UInt64 NULL
16 modulo(UInt16, UInt8) :: UInt8
17 modulo(UInt16 NULL, UInt8 NULL) :: UInt8 NULL
18 modulo(UInt16, UInt16) :: UInt16
19 modulo(UInt16 NULL, UInt16 NULL) :: UInt16 NULL
20 modulo(UInt16, UInt32) :: UInt32
21 modulo(UInt16 NULL, UInt32 NULL) :: UInt32 NULL
22 modulo(UInt16, UInt64) :: UInt64
23 modulo(UInt16 NULL, UInt64 NULL) :: UInt64 NULL
24 modulo(UInt16, Int8) :: UInt8
25 modulo(UInt16 NULL, Int8 NULL) :: UInt8 NULL
26 modulo(UInt16, Int16) :: UInt16
27 modulo(UInt16 NULL, Int16 NULL) :: UInt16 NULL
28 modulo(UInt16, Int32) :: UInt32
29 modulo(UInt16 NULL, Int32 NULL) :: UInt32 NULL
30 modulo(UInt16, Int64) :: UInt64
31 modulo(UInt16 NULL, Int64 NULL) :: UInt64 NULL
32 modulo(UInt32, UInt8) :: UInt8
33 modulo(UInt32 NULL, UInt8 NULL) :: UInt8 NULL
34 modulo(UInt32, UInt16) :: UInt16
35 modulo(UInt32 NULL, UInt16 NULL) :: UInt16 NULL
36 modulo(UInt32, UInt32) :: UInt32
37 modulo(UInt32 NULL, UInt32 NULL) :: UInt32 NULL
38 modulo(UInt32, UInt64) :: UInt64
39 modulo(UInt32 NULL, UInt64 NULL) :: UInt64 NULL
40 modulo(UInt32, Int8) :: UInt8
41 modulo(UInt32 NULL, Int8 NULL) :: UInt8 NULL
42 modulo(UInt32, Int16) :: UInt16
43 modulo(UInt32 NULL, Int16 NULL) :: UInt16 NULL
44 modulo(UInt32, Int32) :: UInt32
45 modulo(UInt32 NULL, Int32 NULL) :: UInt32 NULL
46 modulo(UInt32, Int64) :: UInt64
47 modulo(UInt32 NULL, Int64 NULL) :: UInt64 NULL
48 modulo(UInt64, UInt8) :: UInt8
49 modulo(UInt64 NULL, UInt8 NULL) :: UInt8 NULL
50 modulo(UInt64, UInt16) :: UInt16
51 modulo(UInt64 NULL, UInt16 NULL) :: UInt16 NULL
52 modulo(UInt64, UInt32) :: UInt32
53 modulo(UInt64 NULL, UInt32 NULL) :: UInt32 NULL
54 modulo(UInt64, UInt64) :: UInt64
55 modulo(UInt64 NULL, UInt64 NULL) :: UInt64 NULL
56 modulo(UInt64, Int8) :: UInt8
57 modulo(UInt64 NULL, Int8 NULL) :: UInt8 NULL
58 modulo(UInt64, Int16) :: UInt16
59 modulo(UInt64 NULL, Int16 NULL) :: UInt16 NULL
60 modulo(UInt64, Int32) :: UInt32
61 modulo(UInt64 NULL, Int32 NULL) :: UInt32 NULL
62 modulo(UInt64, Int64) :: UInt64
63 modulo(UInt64 NULL, Int64 NULL) :: UInt64 NULL
64 modulo(Int8, UInt8) :: Int16
65 modulo(Int8 NULL, UInt8 NULL) :: Int16 NULL
66 modulo(Int8, UInt16) :: Int32
67 modulo(Int8 NULL, UInt16 NULL) :: Int32 NULL
68 modulo(Int8, UInt32) :: Int64
69 modulo(Int8 NULL, UInt32 NULL) :: Int64 NULL
70 modulo(Int8, UInt64) :: Int64
71 modulo(Int8 NULL, UInt64 NULL) :: Int64 NULL
72 modulo(Int8, Int8) :: Int16
73 modulo(Int8 NULL, Int8 NULL) :: Int16 NULL
74 modulo(Int8, Int16) :: Int32
75 modulo(Int8 NULL, Int16 NULL) :: Int32 NULL
76 modulo(Int8, Int32) :: Int64
77 modulo(Int8 NULL, Int32 NULL) :: Int64 NULL
78 modulo(Int8, Int64) :: Int64
79 modulo(Int8 NULL, Int64 NULL) :: Int64 NULL
80 modulo(Int16, UInt8) :: Int16
81 modulo(Int16 NULL, UInt8 NULL) :: Int16 NULL
82 modulo(Int16, UInt16) :: Int32
83 modulo(Int16 NULL, UInt16 NULL) :: Int32 NULL
84 modulo(Int16, UInt32) :: Int64
85 modulo(Int16 NULL, UInt32 NULL) :: Int64 NULL
86 modulo(Int16, UInt64) :: Int64
87 modulo(Int16 NULL, UInt64 NULL) :: Int64 NULL
88 modulo(Int16, Int8) :: Int16
89 modulo(Int16 NULL, Int8 NULL) :: Int16 NULL
90 modulo(Int16, Int16) :: Int32
91 modulo(Int16 NULL, Int16 NULL) :: Int32 NULL
92 modulo(Int16, Int32) :: Int64
93 modulo(Int16 NULL, Int32 NULL) :: Int64 NULL
94 modulo(Int16, Int64) :: Int64
95 modulo(Int16 NULL, Int64 NULL) :: Int64 NULL
96 modulo(Int32, UInt8) :: Int16
97 modulo(Int32 NULL, UInt8 NULL) :: Int16 NULL
98 modulo(Int32, UInt16) :: Int32
99 modulo(Int32 NULL, UInt16 NULL) :: Int32 NULL
100 modulo(Int32, UInt32) :: Int64
101 modulo(Int32 NULL, UInt32 NULL) :: Int64 NULL
102 modulo(Int32, UInt64) :: Int64
103 modulo(Int32 NULL, UInt64 NULL) :: Int64 NULL
104 modulo(Int32, Int8) :: Int16
105 modulo(Int32 NULL, Int8 NULL) :: Int16 NULL
106 modulo(Int32, Int16) :: Int32
107 modulo(Int32 NULL, Int16 NULL) :: Int32 NULL
108 modulo(Int32, Int32) :: Int64
109 modulo(Int32 NULL, Int32 NULL) :: Int64 NULL
110 modulo(Int32, Int64) :: Int64
111 modulo(Int32 NULL, Int64 NULL) :: Int64 NULL
112 modulo(Int64, UInt8) :: Int16
113 modulo(Int64 NULL, UInt8 NULL) :: Int16 NULL
114 modulo(Int64, UInt16) :: Int32
115 modulo(Int64 NULL, UInt16 NULL) :: Int32 NULL
116 modulo(Int64, UInt32) :: Int64
117 modulo(Int64 NULL, UInt32 NULL) :: Int64 NULL
118 modulo(Int64, UInt64) :: Int64
119 modulo(Int64 NULL, UInt64 NULL) :: Int64 NULL
120 modulo(Int64, Int8) :: Int16
121 modulo(Int64 NULL, Int8 NULL) :: Int16 NULL
122 modulo(Int64, Int16) :: Int32
123 modulo(Int64 NULL, Int16 NULL) :: Int32 NULL
124 modulo(Int64, Int32) :: Int64
125 modulo(Int64 NULL, Int32 NULL) :: Int64 NULL
126 modulo(Int64, Int64) :: Int64
127 modulo(Int64 NULL, Int64 NULL) :: Int64 NULL
128 modulo(UInt8, Float32) :: Float64
129 modulo(UInt8 NULL, Float32 NULL) :: Float64 NULL
130 modulo(UInt8, Float64) :: Float64
131 modulo(UInt8 NULL, Float64 NULL) :: Float64 NULL
132 modulo(UInt16, Float32) :: Float64
133 modulo(UInt16 NULL, Float32 NULL) :: Float64 NULL
134 modulo(UInt16, Float64) :: Float64
135 modulo(UInt16 NULL, Float64 NULL) :: Float64 NULL
136 modulo(UInt32, Float32) :: Float64
137 modulo(UInt32 NULL, Float32 NULL) :: Float64 NULL
138 modulo(UInt32, Float64) :: Float64
139 modulo(UInt32 NULL, Float64 NULL) :: Float64 NULL
140 modulo(UInt64, Float32) :: Float64
141 modulo(UInt64 NULL, Float32 NULL) :: Float64 NULL
142 modulo(UInt64, Float64) :: Float64
143 modulo(UInt64 NULL, Float64 NULL) :: Float64 NULL
144 modulo(Int8, Float32) :: Float64
145 modulo(Int8 NULL, Float32 NULL) :: Float64 NULL
146 modulo(Int8, Float64) :: Float64
147 modulo(Int8 NULL, Float64 NULL) :: Float64 NULL
148 modulo(Int16, Float32) :: Float64
149 modulo(Int16 NULL, Float32 NULL) :: Float64 NULL
150 modulo(Int16, Float64) :: Float64
151 modulo(Int16 NULL, Float64 NULL) :: Float64 NULL
152 modulo(Int32, Float32) :: Float64
153 modulo(Int32 NULL, Float32 NULL) :: Float64 NULL
154 modulo(Int32, Float64) :: Float64
155 modulo(Int32 NULL, Float64 NULL) :: Float64 NULL
156 modulo(Int64, Float32) :: Float64
157 modulo(Int64 NULL, Float32 NULL) :: Float64 NULL
158 modulo(Int64, Float64) :: Float64
159 modulo(Int64 NULL, Float64 NULL) :: Float64 NULL
160 modulo(Float32, UInt8) :: Float64
161 modulo(Float32 NULL, UInt8 NULL) :: Float64 NULL
162 modulo(Float32, UInt16) :: Float64
163 modulo(Float32 NULL, UInt16 NULL) :: Float64 NULL
164 modulo(Float32, UInt32) :: Float64
165 modulo(Float32 NULL, UInt32 NULL) :: Float64 NULL
166 modulo(Float32, UInt64) :: Float64
167 modulo(Float32 NULL, UInt64 NULL) :: Float64 NULL
168 modulo(Float32, Int8) :: Float64
169 modulo(Float32 NULL, Int8 NULL) :: Float64 NULL
170 modulo(Float32, Int16) :: Float64
171 modulo(Float32 NULL, Int16 NULL) :: Float64 NULL
172 modulo(Float32, Int32) :: Float64
173 modulo(Float32 NULL, Int32 NULL) :: Float64 NULL
174 modulo(Float32, Int64) :: Float64
175 modulo(Float32 NULL, Int64 NULL) :: Float64 NULL
176 modulo(Float64, UInt8) :: Float64
177 modulo(Float64 NULL, UInt8 NULL) :: Float64 NULL
178 modulo(Float64, UInt16) :: Float64
179 modulo(Float64 NULL, UInt16 NULL) :: Float64 NULL
180 modulo(Float64, UInt32) :: Float64
181 modulo(Float64 NULL, UInt32 NULL) :: Float64 NULL
182 modulo(Float64, UInt64) :: Float64
183 modulo(Float64 NULL, UInt64 NULL) :: Float64 NULL
184 modulo(Float64, Int8) :: Float64
185 modulo(Float64 NULL, Int8 NULL) :: Float64 NULL
186 modulo(Float64, Int16) :: Float64
187 modulo(Float64 NULL, Int16 NULL) :: Float64 NULL
188 modulo(Float64, Int32) :: Float64
189 modulo(Float64 NULL, Int32 NULL) :: Float64 NULL
190 modulo(Float64, Int64) :: Float64
191 modulo(Float64 NULL, Int64 NULL) :: Float64 NULL
192 modulo(Float32, Float32) :: Float64
193 modulo(Float32 NULL, Float32 NULL) :: Float64 NULL
194 modulo(Float32, Float64) :: Float64
195 modulo(Float32 NULL, Float64 NULL) :: Float64 NULL
196 modulo(Float64, Float32) :: Float64
197 modulo(Float64 NULL, Float32 NULL) :: Float64 NULL
198 modulo(Float64, Float64) :: Float64
199 modulo(Float64 NULL, Float64 NULL) :: Float64 NULL
0 months_between(Date, Date) :: Float64
1 months_between(Date NULL, Date NULL) :: Float64 NULL
2 months_between(Timestamp, Timestamp) :: Float64
3 months_between(Timestamp NULL, Timestamp NULL) :: Float64 NULL
0 multiply FACTORY
1 multiply(UInt8, UInt8) :: UInt16
2 multiply(UInt8 NULL, UInt8 NULL) :: UInt16 NULL
3 multiply(UInt8, UInt16) :: UInt32
4 multiply(UInt8 NULL, UInt16 NULL) :: UInt32 NULL
5 multiply(UInt8, UInt32) :: UInt64
6 multiply(UInt8 NULL, UInt32 NULL) :: UInt64 NULL
7 multiply(UInt8, UInt64) :: UInt64
8 multiply(UInt8 NULL, UInt64 NULL) :: UInt64 NULL
9 multiply(UInt8, Int8) :: Int16
10 multiply(UInt8 NULL, Int8 NULL) :: Int16 NULL
11 multiply(UInt8, Int16) :: Int32
12 multiply(UInt8 NULL, Int16 NULL) :: Int32 NULL
13 multiply(UInt8, Int32) :: Int64
14 multiply(UInt8 NULL, Int32 NULL) :: Int64 NULL
15 multiply(UInt8, Int64) :: Int64
16 multiply(UInt8 NULL, Int64 NULL) :: Int64 NULL
17 multiply(UInt16, UInt8) :: UInt32
18 multiply(UInt16 NULL, UInt8 NULL) :: UInt32 NULL
19 multiply(UInt16, UInt16) :: UInt32
20 multiply(UInt16 NULL, UInt16 NULL) :: UInt32 NULL
21 multiply(UInt16, UInt32) :: UInt64
22 multiply(UInt16 NULL, UInt32 NULL) :: UInt64 NULL
23 multiply(UInt16, UInt64) :: UInt64
24 multiply(UInt16 NULL, UInt64 NULL) :: UInt64 NULL
25 multiply(UInt16, Int8) :: Int32
26 multiply(UInt16 NULL, Int8 NULL) :: Int32 NULL
27 multiply(UInt16, Int16) :: Int32
28 multiply(UInt16 NULL, Int16 NULL) :: Int32 NULL
29 multiply(UInt16, Int32) :: Int64
30 multiply(UInt16 NULL, Int32 NULL) :: Int64 NULL
31 multiply(UInt16, Int64) :: Int64
32 multiply(UInt16 NULL, Int64 NULL) :: Int64 NULL
33 multiply(UInt32, UInt8) :: UInt64
34 multiply(UInt32 NULL, UInt8 NULL) :: UInt64 NULL
35 multiply(UInt32, UInt16) :: UInt64
36 multiply(UInt32 NULL, UInt16 NULL) :: UInt64 NULL
37 multiply(UInt32, UInt32) :: UInt64
38 multiply(UInt32 NULL, UInt32 NULL) :: UInt64 NULL
39 multiply(UInt32, UInt64) :: UInt64
40 multiply(UInt32 NULL, UInt64 NULL) :: UInt64 NULL
41 multiply(UInt32, Int8) :: Int64
42 multiply(UInt32 NULL, Int8 NULL) :: Int64 NULL
43 multiply(UInt32, Int16) :: Int64
44 multiply(UInt32 NULL, Int16 NULL) :: Int64 NULL
45 multiply(UInt32, Int32) :: Int64
46 multiply(UInt32 NULL, Int32 NULL) :: Int64 NULL
47 multiply(UInt32, Int64) :: Int64
48 multiply(UInt32 NULL, Int64 NULL) :: Int64 NULL
49 multiply(UInt64, UInt8) :: UInt64
50 multiply(UInt64 NULL, UInt8 NULL) :: UInt64 NULL
51 multiply(UInt64, UInt16) :: UInt64
52 multiply(UInt64 NULL, UInt16 NULL) :: UInt64 NULL
53 multiply(UInt64, UInt32) :: UInt64
54 multiply(UInt64 NULL, UInt32 NULL) :: UInt64 NULL
55 multiply(UInt64, UInt64) :: UInt64
56 multiply(UInt64 NULL, UInt64 NULL) :: UInt64 NULL
57 multiply(UInt64, Int8) :: Int64
58 multiply(UInt64 NULL, Int8 NULL) :: Int64 NULL
59 multiply(UInt64, Int16) :: Int64
60 multiply(UInt64 NULL, Int16 NULL) :: Int64 NULL
61 multiply(UInt64, Int32) :: Int64
62 multiply(UInt64 NULL, Int32 NULL) :: Int64 NULL
63 multiply(UInt64, Int64) :: Int64
64 multiply(UInt64 NULL, Int64 NULL) :: Int64 NULL
65 multiply(Int8, UInt8) :: Int16
66 multiply(Int8 NULL, UInt8 NULL) :: Int16 NULL
67 multiply(Int8, UInt16) :: Int32
68 multiply(Int8 NULL, UInt16 NULL) :: Int32 NULL
69 multiply(Int8, UInt32) :: Int64
70 multiply(Int8 NULL, UInt32 NULL) :: Int64 NULL
71 multiply(Int8, UInt64) :: Int64
72 multiply(Int8 NULL, UInt64 NULL) :: Int64 NULL
73 multiply(Int8, Int8) :: Int16
74 multiply(Int8 NULL, Int8 NULL) :: Int16 NULL
75 multiply(Int8, Int16) :: Int32
76 multiply(Int8 NULL, Int16 NULL) :: Int32 NULL
77 multiply(Int8, Int32) :: Int64
78 multiply(Int8 NULL, Int32 NULL) :: Int64 NULL
79 multiply(Int8, Int64) :: Int64
80 multiply(Int8 NULL, Int64 NULL) :: Int64 NULL
81 multiply(Int16, UInt8) :: Int32
82 multiply(Int16 NULL, UInt8 NULL) :: Int32 NULL
83 multiply(Int16, UInt16) :: Int32
84 multiply(Int16 NULL, UInt16 NULL) :: Int32 NULL
85 multiply(Int16, UInt32) :: Int64
86 multiply(Int16 NULL, UInt32 NULL) :: Int64 NULL
87 multiply(Int16, UInt64) :: Int64
88 multiply(Int16 NULL, UInt64 NULL) :: Int64 NULL
89 multiply(Int16, Int8) :: Int32
90 multiply(Int16 NULL, Int8 NULL) :: Int32 NULL
91 multiply(Int16, Int16) :: Int32
92 multiply(Int16 NULL, Int16 NULL) :: Int32 NULL
93 multiply(Int16, Int32) :: Int64
94 multiply(Int16 NULL, Int32 NULL) :: Int64 NULL
95 multiply(Int16, Int64) :: Int64
96 multiply(Int16 NULL, Int64 NULL) :: Int64 NULL
97 multiply(Int32, UInt8) :: Int64
98 multiply(Int32 NULL, UInt8 NULL) :: Int64 NULL
99 multiply(Int32, UInt16) :: Int64
100 multiply(Int32 NULL, UInt16 NULL) :: Int64 NULL
101 multiply(Int32, UInt32) :: Int64
102 multiply(Int32 NULL, UInt32 NULL) :: Int64 NULL
103 multiply(Int32, UInt64) :: Int64
104 multiply(Int32 NULL, UInt64 NULL) :: Int64 NULL
105 multiply(Int32, Int8) :: Int64
106 multiply(Int32 NULL, Int8 NULL) :: Int64 NULL
107 multiply(Int32, Int16) :: Int64
108 multiply(Int32 NULL, Int16 NULL) :: Int64 NULL
109 multiply(Int32, Int32) :: Int64
110 multiply(Int32 NULL, Int32 NULL) :: Int64 NULL
111 multiply(Int32, Int64) :: Int64
112 multiply(Int32 NULL, Int64 NULL) :: Int64 NULL
113 multiply(Int64, UInt8) :: Int64
114 multiply(Int64 NULL, UInt8 NULL) :: Int64 NULL
115 multiply(Int64, UInt16) :: Int64
116 multiply(Int64 NULL, UInt16 NULL) :: Int64 NULL
117 multiply(Int64, UInt32) :: Int64
118 multiply(Int64 NULL, UInt32 NULL) :: Int64 NULL
119 multiply(Int64, UInt64) :: Int64
120 multiply(Int64 NULL, UInt64 NULL) :: Int64 NULL
121 multiply(Int64, Int8) :: Int64
122 multiply(Int64 NULL, Int8 NULL) :: Int64 NULL
123 multiply(Int64, Int16) :: Int64
124 multiply(Int64 NULL, Int16 NULL) :: Int64 NULL
125 multiply(Int64, Int32) :: Int64
126 multiply(Int64 NULL, Int32 NULL) :: Int64 NULL
127 multiply(Int64, Int64) :: Int64
128 multiply(Int64 NULL, Int64 NULL) :: Int64 NULL
129 multiply(UInt8, Float32) :: Float64
130 multiply(UInt8 NULL, Float32 NULL) :: Float64 NULL
131 multiply(UInt8, Float64) :: Float64
132 multiply(UInt8 NULL, Float64 NULL) :: Float64 NULL
133 multiply(UInt16, Float32) :: Float64
134 multiply(UInt16 NULL, Float32 NULL) :: Float64 NULL
135 multiply(UInt16, Float64) :: Float64
136 multiply(UInt16 NULL, Float64 NULL) :: Float64 NULL
137 multiply(UInt32, Float32) :: Float64
138 multiply(UInt32 NULL, Float32 NULL) :: Float64 NULL
139 multiply(UInt32, Float64) :: Float64
140 multiply(UInt32 NULL, Float64 NULL) :: Float64 NULL
141 multiply(UInt64, Float32) :: Float64
142 multiply(UInt64 NULL, Float32 NULL) :: Float64 NULL
143 multiply(UInt64, Float64) :: Float64
144 multiply(UInt64 NULL, Float64 NULL) :: Float64 NULL
145 multiply(Int8, Float32) :: Float64
146 multiply(Int8 NULL, Float32 NULL) :: Float64 NULL
147 multiply(Int8, Float64) :: Float64
148 multiply(Int8 NULL, Float64 NULL) :: Float64 NULL
149 multiply(Int16, Float32) :: Float64
150 multiply(Int16 NULL, Float32 NULL) :: Float64 NULL
151 multiply(Int16, Float64) :: Float64
152 multiply(Int16 NULL, Float64 NULL) :: Float64 NULL
153 multiply(Int32, Float32) :: Float64
154 multiply(Int32 NULL, Float32 NULL) :: Float64 NULL
155 multiply(Int32, Float64) :: Float64
156 multiply(Int32 NULL, Float64 NULL) :: Float64 NULL
157 multiply(Int64, Float32) :: Float64
158 multiply(Int64 NULL, Float32 NULL) :: Float64 NULL
159 multiply(Int64, Float64) :: Float64
160 multiply(Int64 NULL, Float64 NULL) :: Float64 NULL
161 multiply(Float32, UInt8) :: Float64
162 multiply(Float32 NULL, UInt8 NULL) :: Float64 NULL
163 multiply(Float32, UInt16) :: Float64
164 multiply(Float32 NULL, UInt16 NULL) :: Float64 NULL
165 multiply(Float32, UInt32) :: Float64
166 multiply(Float32 NULL, UInt32 NULL) :: Float64 NULL
167 multiply(Float32, UInt64) :: Float64
168 multiply(Float32 NULL, UInt64 NULL) :: Float64 NULL
169 multiply(Float32, Int8) :: Float64
170 multiply(Float32 NULL, Int8 NULL) :: Float64 NULL
171 multiply(Float32, Int16) :: Float64
172 multiply(Float32 NULL, Int16 NULL) :: Float64 NULL
173 multiply(Float32, Int32) :: Float64
174 multiply(Float32 NULL, Int32 NULL) :: Float64 NULL
175 multiply(Float32, Int64) :: Float64
176 multiply(Float32 NULL, Int64 NULL) :: Float64 NULL
177 multiply(Float64, UInt8) :: Float64
178 multiply(Float64 NULL, UInt8 NULL) :: Float64 NULL
179 multiply(Float64, UInt16) :: Float64
180 multiply(Float64 NULL, UInt16 NULL) :: Float64 NULL
181 multiply(Float64, UInt32) :: Float64
182 multiply(Float64 NULL, UInt32 NULL) :: Float64 NULL
183 multiply(Float64, UInt64) :: Float64
184 multiply(Float64 NULL, UInt64 NULL) :: Float64 NULL
185 multiply(Float64, Int8) :: Float64
186 multiply(Float64 NULL, Int8 NULL) :: Float64 NULL
187 multiply(Float64, Int16) :: Float64
188 multiply(Float64 NULL, Int16 NULL) :: Float64 NULL
189 multiply(Float64, Int32) :: Float64
190 multiply(Float64 NULL, Int32 NULL) :: Float64 NULL
191 multiply(Float64, Int64) :: Float64
192 multiply(Float64 NULL, Int64 NULL) :: Float64 NULL
193 multiply(Float32, Float32) :: Float64
194 multiply(Float32 NULL, Float32 NULL) :: Float64 NULL
195 multiply(Float32, Float64) :: Float64
196 multiply(Float32 NULL, Float64 NULL) :: Float64 NULL
197 multiply(Float64, Float32) :: Float64
198 multiply(Float64 NULL, Float32 NULL) :: Float64 NULL
199 multiply(Float64, Float64) :: Float64
200 multiply(Float64 NULL, Float64 NULL) :: Float64 NULL
201 multiply(Int64, Interval) :: Interval
202 multiply(Int64 NULL, Interval NULL) :: Interval NULL
203 multiply(Interval, Int64) :: Interval
204 multiply(Interval NULL, Int64 NULL) :: Interval NULL
0 not(Boolean) :: Boolean
1 not(Boolean NULL) :: Boolean NULL
0 noteq(Variant, Variant) :: Boolean
1 noteq(Variant NULL, Variant NULL) :: Boolean NULL
2 noteq(String, String) :: Boolean
3 noteq(String NULL, String NULL) :: Boolean NULL
4 noteq(Date, Date) :: Boolean
5 noteq(Date NULL, Date NULL) :: Boolean NULL
6 noteq(Timestamp, Timestamp) :: Boolean
7 noteq(Timestamp NULL, Timestamp NULL) :: Boolean NULL
8 noteq(UInt8, UInt8) :: Boolean
9 noteq(UInt8 NULL, UInt8 NULL) :: Boolean NULL
10 noteq(Int8, Int8) :: Boolean
11 noteq(Int8 NULL, Int8 NULL) :: Boolean NULL
12 noteq(UInt16, UInt16) :: Boolean
13 noteq(UInt16 NULL, UInt16 NULL) :: Boolean NULL
14 noteq(Int16, Int16) :: Boolean
15 noteq(Int16 NULL, Int16 NULL) :: Boolean NULL
16 noteq(UInt32, UInt32) :: Boolean
17 noteq(UInt32 NULL, UInt32 NULL) :: Boolean NULL
18 noteq(Int32, Int32) :: Boolean
19 noteq(Int32 NULL, Int32 NULL) :: Boolean NULL
20 noteq(UInt64, UInt64) :: Boolean
21 noteq(UInt64 NULL, UInt64 NULL) :: Boolean NULL
22 noteq(Int64, Int64) :: Boolean
23 noteq(Int64 NULL, Int64 NULL) :: Boolean NULL
24 noteq FACTORY
25 noteq(Float32, Float32) :: Boolean
26 noteq(Float32 NULL, Float32 NULL) :: Boolean NULL
27 noteq(Float64, Float64) :: Boolean
28 noteq(Float64 NULL, Float64 NULL) :: Boolean NULL
29 noteq(String, UInt8) :: Boolean
30 noteq(String NULL, UInt8 NULL) :: Boolean NULL
31 noteq(UInt8, String) :: Boolean
32 noteq(UInt8 NULL, String NULL) :: Boolean NULL
33 noteq(String, UInt16) :: Boolean
34 noteq(String NULL, UInt16 NULL) :: Boolean NULL
35 noteq(UInt16, String) :: Boolean
36 noteq(UInt16 NULL, String NULL) :: Boolean NULL
37 noteq(String, UInt32) :: Boolean
38 noteq(String NULL, UInt32 NULL) :: Boolean NULL
39 noteq(UInt32, String) :: Boolean
40 noteq(UInt32 NULL, String NULL) :: Boolean NULL
41 noteq(String, UInt64) :: Boolean
42 noteq(String NULL, UInt64 NULL) :: Boolean NULL
43 noteq(UInt64, String) :: Boolean
44 noteq(UInt64 NULL, String NULL) :: Boolean NULL
45 noteq(String, Int8) :: Boolean
46 noteq(String NULL, Int8 NULL) :: Boolean NULL
47 noteq(Int8, String) :: Boolean
48 noteq(Int8 NULL, String NULL) :: Boolean NULL
49 noteq(String, Int16) :: Boolean
50 noteq(String NULL, Int16 NULL) :: Boolean NULL
51 noteq(Int16, String) :: Boolean
52 noteq(Int16 NULL, String NULL) :: Boolean NULL
53 noteq(String, Int32) :: Boolean
54 noteq(String NULL, Int32 NULL) :: Boolean NULL
55 noteq(Int32, String) :: Boolean
56 noteq(Int32 NULL, String NULL) :: Boolean NULL
57 noteq(String, Int64) :: Boolean
58 noteq(String NULL, Int64 NULL) :: Boolean NULL
59 noteq(Int64, String) :: Boolean
60 noteq(Int64 NULL, String NULL) :: Boolean NULL
61 noteq(String, Float32) :: Boolean
62 noteq(String NULL, Float32 NULL) :: Boolean NULL
63 noteq(Float32, String) :: Boolean
64 noteq(Float32 NULL, String NULL) :: Boolean NULL
65 noteq(String, Float64) :: Boolean
66 noteq(String NULL, Float64 NULL) :: Boolean NULL
67 noteq(Float64, String) :: Boolean
68 noteq(Float64 NULL, String NULL) :: Boolean NULL
69 noteq(Boolean, Boolean) :: Boolean
70 noteq(Boolean NULL, Boolean NULL) :: Boolean NULL
71 noteq(Array(Nothing), Array(Nothing)) :: Boolean
72 noteq(Array(Nothing) NULL, Array(Nothing) NULL) :: Boolean NULL
73 noteq(Array(T0), Array(T0)) :: Boolean
74 noteq(Array(T0) NULL, Array(T0) NULL) :: Boolean NULL
75 noteq FACTORY
76 noteq(Interval, Interval) :: Boolean
77 noteq(Interval NULL, Interval NULL) :: Boolean NULL
0 now() :: Timestamp
0 oct(Int64) :: String
1 oct(Int64 NULL) :: String NULL
0 octet_length(String) :: UInt64
1 octet_length(String NULL) :: UInt64 NULL
0 or(Boolean, Boolean) :: Boolean
1 or(Boolean NULL, Boolean NULL) :: Boolean NULL
0 ord(String) :: UInt64
1 ord(String NULL) :: UInt64 NULL
0 parse_json(Variant) :: Variant
1 parse_json(Variant NULL) :: Variant NULL
2 parse_json(String) :: Variant
3 parse_json(String NULL) :: Variant NULL
0 pi() :: Float64
0 plus FACTORY
1 plus(UInt8, UInt8) :: UInt16
2 plus(UInt8 NULL, UInt8 NULL) :: UInt16 NULL
3 plus(UInt8, UInt16) :: UInt32
4 plus(UInt8 NULL, UInt16 NULL) :: UInt32 NULL
5 plus(UInt8, UInt32) :: UInt64
6 plus(UInt8 NULL, UInt32 NULL) :: UInt64 NULL
7 plus(UInt8, UInt64) :: UInt64
8 plus(UInt8 NULL, UInt64 NULL) :: UInt64 NULL
9 plus(UInt8, Int8) :: Int16
10 plus(UInt8 NULL, Int8 NULL) :: Int16 NULL
11 plus(UInt8, Int16) :: Int32
12 plus(UInt8 NULL, Int16 NULL) :: Int32 NULL
13 plus(UInt8, Int32) :: Int64
14 plus(UInt8 NULL, Int32 NULL) :: Int64 NULL
15 plus(UInt8, Int64) :: Int64
16 plus(UInt8 NULL, Int64 NULL) :: Int64 NULL
17 plus(UInt16, UInt8) :: UInt32
18 plus(UInt16 NULL, UInt8 NULL) :: UInt32 NULL
19 plus(UInt16, UInt16) :: UInt32
20 plus(UInt16 NULL, UInt16 NULL) :: UInt32 NULL
21 plus(UInt16, UInt32) :: UInt64
22 plus(UInt16 NULL, UInt32 NULL) :: UInt64 NULL
23 plus(UInt16, UInt64) :: UInt64
24 plus(UInt16 NULL, UInt64 NULL) :: UInt64 NULL
25 plus(UInt16, Int8) :: Int32
26 plus(UInt16 NULL, Int8 NULL) :: Int32 NULL
27 plus(UInt16, Int16) :: Int32
28 plus(UInt16 NULL, Int16 NULL) :: Int32 NULL
29 plus(UInt16, Int32) :: Int64
30 plus(UInt16 NULL, Int32 NULL) :: Int64 NULL
31 plus(UInt16, Int64) :: Int64
32 plus(UInt16 NULL, Int64 NULL) :: Int64 NULL
33 plus(UInt32, UInt8) :: UInt64
34 plus(UInt32 NULL, UInt8 NULL) :: UInt64 NULL
35 plus(UInt32, UInt16) :: UInt64
36 plus(UInt32 NULL, UInt16 NULL) :: UInt64 NULL
37 plus(UInt32, UInt32) :: UInt64
38 plus(UInt32 NULL, UInt32 NULL) :: UInt64 NULL
39 plus(UInt32, UInt64) :: UInt64
40 plus(UInt32 NULL, UInt64 NULL) :: UInt64 NULL
41 plus(UInt32, Int8) :: Int64
42 plus(UInt32 NULL, Int8 NULL) :: Int64 NULL
43 plus(UInt32, Int16) :: Int64
44 plus(UInt32 NULL, Int16 NULL) :: Int64 NULL
45 plus(UInt32, Int32) :: Int64
46 plus(UInt32 NULL, Int32 NULL) :: Int64 NULL
47 plus(UInt32, Int64) :: Int64
48 plus(UInt32 NULL, Int64 NULL) :: Int64 NULL
49 plus(UInt64, UInt8) :: UInt64
50 plus(UInt64 NULL, UInt8 NULL) :: UInt64 NULL
51 plus(UInt64, UInt16) :: UInt64
52 plus(UInt64 NULL, UInt16 NULL) :: UInt64 NULL
53 plus(UInt64, UInt32) :: UInt64
54 plus(UInt64 NULL, UInt32 NULL) :: UInt64 NULL
55 plus(UInt64, UInt64) :: UInt64
56 plus(UInt64 NULL, UInt64 NULL) :: UInt64 NULL
57 plus(UInt64, Int8) :: Int64
58 plus(UInt64 NULL, Int8 NULL) :: Int64 NULL
59 plus(UInt64, Int16) :: Int64
60 plus(UInt64 NULL, Int16 NULL) :: Int64 NULL
61 plus(UInt64, Int32) :: Int64
62 plus(UInt64 NULL, Int32 NULL) :: Int64 NULL
63 plus(UInt64, Int64) :: Int64
64 plus(UInt64 NULL, Int64 NULL) :: Int64 NULL
65 plus(Int8, UInt8) :: Int16
66 plus(Int8 NULL, UInt8 NULL) :: Int16 NULL
67 plus(Int8, UInt16) :: Int32
68 plus(Int8 NULL, UInt16 NULL) :: Int32 NULL
69 plus(Int8, UInt32) :: Int64
70 plus(Int8 NULL, UInt32 NULL) :: Int64 NULL
71 plus(Int8, UInt64) :: Int64
72 plus(Int8 NULL, UInt64 NULL) :: Int64 NULL
73 plus(Int8, Int8) :: Int16
74 plus(Int8 NULL, Int8 NULL) :: Int16 NULL
75 plus(Int8, Int16) :: Int32
76 plus(Int8 NULL, Int16 NULL) :: Int32 NULL
77 plus(Int8, Int32) :: Int64
78 plus(Int8 NULL, Int32 NULL) :: Int64 NULL
79 plus(Int8, Int64) :: Int64
80 plus(Int8 NULL, Int64 NULL) :: Int64 NULL
81 plus(Int16, UInt8) :: Int32
82 plus(Int16 NULL, UInt8 NULL) :: Int32 NULL
83 plus(Int16, UInt16) :: Int32
84 plus(Int16 NULL, UInt16 NULL) :: Int32 NULL
85 plus(Int16, UInt32) :: Int64
86 plus(Int16 NULL, UInt32 NULL) :: Int64 NULL
87 plus(Int16, UInt64) :: Int64
88 plus(Int16 NULL, UInt64 NULL) :: Int64 NULL
89 plus(Int16, Int8) :: Int32
90 plus(Int16 NULL, Int8 NULL) :: Int32 NULL
91 plus(Int16, Int16) :: Int32
92 plus(Int16 NULL, Int16 NULL) :: Int32 NULL
93 plus(Int16, Int32) :: Int64
94 plus(Int16 NULL, Int32 NULL) :: Int64 NULL
95 plus(Int16, Int64) :: Int64
96 plus(Int16 NULL, Int64 NULL) :: Int64 NULL
97 plus(Int32, UInt8) :: Int64
98 plus(Int32 NULL, UInt8 NULL) :: Int64 NULL
99 plus(Int32, UInt16) :: Int64
100 plus(Int32 NULL, UInt16 NULL) :: Int64 NULL
101 plus(Int32, UInt32) :: Int64
102 plus(Int32 NULL, UInt32 NULL) :: Int64 NULL
103 plus(Int32, UInt64) :: Int64
104 plus(Int32 NULL, UInt64 NULL) :: Int64 NULL
105 plus(Int32, Int8) :: Int64
106 plus(Int32 NULL, Int8 NULL) :: Int64 NULL
107 plus(Int32, Int16) :: Int64
108 plus(Int32 NULL, Int16 NULL) :: Int64 NULL
109 plus(Int32, Int32) :: Int64
110 plus(Int32 NULL, Int32 NULL) :: Int64 NULL
111 plus(Int32, Int64) :: Int64
112 plus(Int32 NULL, Int64 NULL) :: Int64 NULL
113 plus(Int64, UInt8) :: Int64
114 plus(Int64 NULL, UInt8 NULL) :: Int64 NULL
115 plus(Int64, UInt16) :: Int64
116 plus(Int64 NULL, UInt16 NULL) :: Int64 NULL
117 plus(Int64, UInt32) :: Int64
118 plus(Int64 NULL, UInt32 NULL) :: Int64 NULL
119 plus(Int64, UInt64) :: Int64
120 plus(Int64 NULL, UInt64 NULL) :: Int64 NULL
121 plus(Int64, Int8) :: Int64
122 plus(Int64 NULL, Int8 NULL) :: Int64 NULL
123 plus(Int64, Int16) :: Int64
124 plus(Int64 NULL, Int16 NULL) :: Int64 NULL
125 plus(Int64, Int32) :: Int64
126 plus(Int64 NULL, Int32 NULL) :: Int64 NULL
127 plus(Int64, Int64) :: Int64
128 plus(Int64 NULL, Int64 NULL) :: Int64 NULL
129 plus(UInt8, Float32) :: Float64
130 plus(UInt8 NULL, Float32 NULL) :: Float64 NULL
131 plus(UInt8, Float64) :: Float64
132 plus(UInt8 NULL, Float64 NULL) :: Float64 NULL
133 plus(UInt16, Float32) :: Float64
134 plus(UInt16 NULL, Float32 NULL) :: Float64 NULL
135 plus(UInt16, Float64) :: Float64
136 plus(UInt16 NULL, Float64 NULL) :: Float64 NULL
137 plus(UInt32, Float32) :: Float64
138 plus(UInt32 NULL, Float32 NULL) :: Float64 NULL
139 plus(UInt32, Float64) :: Float64
140 plus(UInt32 NULL, Float64 NULL) :: Float64 NULL
141 plus(UInt64, Float32) :: Float64
142 plus(UInt64 NULL, Float32 NULL) :: Float64 NULL
143 plus(UInt64, Float64) :: Float64
144 plus(UInt64 NULL, Float64 NULL) :: Float64 NULL
145 plus(Int8, Float32) :: Float64
146 plus(Int8 NULL, Float32 NULL) :: Float64 NULL
147 plus(Int8, Float64) :: Float64
148 plus(Int8 NULL, Float64 NULL) :: Float64 NULL
149 plus(Int16, Float32) :: Float64
150 plus(Int16 NULL, Float32 NULL) :: Float64 NULL
151 plus(Int16, Float64) :: Float64
152 plus(Int16 NULL, Float64 NULL) :: Float64 NULL
153 plus(Int32, Float32) :: Float64
154 plus(Int32 NULL, Float32 NULL) :: Float64 NULL
155 plus(Int32, Float64) :: Float64
156 plus(Int32 NULL, Float64 NULL) :: Float64 NULL
157 plus(Int64, Float32) :: Float64
158 plus(Int64 NULL, Float32 NULL) :: Float64 NULL
159 plus(Int64, Float64) :: Float64
160 plus(Int64 NULL, Float64 NULL) :: Float64 NULL
161 plus(Float32, UInt8) :: Float64
162 plus(Float32 NULL, UInt8 NULL) :: Float64 NULL
163 plus(Float32, UInt16) :: Float64
164 plus(Float32 NULL, UInt16 NULL) :: Float64 NULL
165 plus(Float32, UInt32) :: Float64
166 plus(Float32 NULL, UInt32 NULL) :: Float64 NULL
167 plus(Float32, UInt64) :: Float64
168 plus(Float32 NULL, UInt64 NULL) :: Float64 NULL
169 plus(Float32, Int8) :: Float64
170 plus(Float32 NULL, Int8 NULL) :: Float64 NULL
171 plus(Float32, Int16) :: Float64
172 plus(Float32 NULL, Int16 NULL) :: Float64 NULL
173 plus(Float32, Int32) :: Float64
174 plus(Float32 NULL, Int32 NULL) :: Float64 NULL
175 plus(Float32, Int64) :: Float64
176 plus(Float32 NULL, Int64 NULL) :: Float64 NULL
177 plus(Float64, UInt8) :: Float64
178 plus(Float64 NULL, UInt8 NULL) :: Float64 NULL
179 plus(Float64, UInt16) :: Float64
180 plus(Float64 NULL, UInt16 NULL) :: Float64 NULL
181 plus(Float64, UInt32) :: Float64
182 plus(Float64 NULL, UInt32 NULL) :: Float64 NULL
183 plus(Float64, UInt64) :: Float64
184 plus(Float64 NULL, UInt64 NULL) :: Float64 NULL
185 plus(Float64, Int8) :: Float64
186 plus(Float64 NULL, Int8 NULL) :: Float64 NULL
187 plus(Float64, Int16) :: Float64
188 plus(Float64 NULL, Int16 NULL) :: Float64 NULL
189 plus(Float64, Int32) :: Float64
190 plus(Float64 NULL, Int32 NULL) :: Float64 NULL
191 plus(Float64, Int64) :: Float64
192 plus(Float64 NULL, Int64 NULL) :: Float64 NULL
193 plus(Float32, Float32) :: Float64
194 plus(Float32 NULL, Float32 NULL) :: Float64 NULL
195 plus(Float32, Float64) :: Float64
196 plus(Float32 NULL, Float64 NULL) :: Float64 NULL
197 plus(Float64, Float32) :: Float64
198 plus(Float64 NULL, Float32 NULL) :: Float64 NULL
199 plus(Float64, Float64) :: Float64
200 plus(Float64 NULL, Float64 NULL) :: Float64 NULL
201 plus(Date, Int64) :: Date
202 plus(Date NULL, Int64 NULL) :: Date NULL
203 plus(Timestamp, Int64) :: Timestamp
204 plus(Timestamp NULL, Int64 NULL) :: Timestamp NULL
205 plus(Interval, Interval) :: Interval
206 plus(Interval NULL, Interval NULL) :: Interval NULL
207 plus(Timestamp, Interval) :: Timestamp
208 plus(Timestamp NULL, Interval NULL) :: Timestamp NULL
209 plus(Interval, Timestamp) :: Timestamp
210 plus(Interval NULL, Timestamp NULL) :: Timestamp NULL
0 point_in_ellipses FACTORY
0 point_in_polygon FACTORY
1 point_in_polygon FACTORY
2 point_in_polygon FACTORY
0 position(String, String) :: UInt64
1 position(String NULL, String NULL) :: UInt64 NULL
0 pow(Float64, Float64) :: Float64
1 pow(Float64 NULL, Float64 NULL) :: Float64 NULL
0 quote(String) :: String
1 quote(String NULL) :: String NULL
0 radians(Float64) :: Float64
1 radians(Float64 NULL) :: Float64 NULL
0 rand() :: Float64
1 rand(UInt64) :: Float64
2 rand(UInt64 NULL) :: Float64 NULL
0 range(UInt64, UInt64) :: Array(UInt64)
1 range(UInt64 NULL, UInt64 NULL) :: Array(UInt64) NULL
0 range_partition_id(T0, Array(T0)) :: UInt64
1 range_partition_id(T0 NULL, Array(T0) NULL) :: UInt64
0 regexp(String, String) :: Boolean
1 regexp(String NULL, String NULL) :: Boolean NULL
0 regexp_extract(String, String) :: String
1 regexp_extract(String NULL, String NULL) :: String NULL
2 regexp_extract(String, String, UInt32) :: String
3 regexp_extract(String NULL, String NULL, UInt32 NULL) :: String NULL
4 regexp_extract(String, String, Array(String)) :: Map(String, String)
5 regexp_extract(String NULL, String NULL, Array(String) NULL) :: Map(String, String) NULL
0 regexp_extract_all(String, String) :: Array(String)
1 regexp_extract_all(String NULL, String NULL) :: Array(String) NULL
2 regexp_extract_all(String, String, UInt32) :: Array(String)
3 regexp_extract_all(String NULL, String NULL, UInt32 NULL) :: Array(String) NULL
0 regexp_instr FACTORY
0 regexp_like FACTORY
0 regexp_replace FACTORY
0 regexp_split_to_array(String, String) :: Array(String)
1 regexp_split_to_array(String NULL, String NULL) :: Array(String) NULL
2 regexp_split_to_array(String, String, String) :: Array(String)
3 regexp_split_to_array(String NULL, String NULL, String NULL) :: Array(String) NULL
0 regexp_split_to_table FACTORY
0 regexp_substr FACTORY
0 repeat(String, UInt64) :: String
1 repeat(String NULL, UInt64 NULL) :: String NULL
0 replace(String, String, String) :: String
1 replace(String NULL, String NULL, String NULL) :: String NULL
0 reverse(String) :: String
1 reverse(String NULL) :: String NULL
0 right(String, UInt64) :: String
1 right(String NULL, UInt64 NULL) :: String NULL
0 round FACTORY
1 round(UInt8) :: Float64
2 round(UInt8 NULL) :: Float64 NULL
3 round(UInt8, Int64) :: Float64
4 round(UInt8 NULL, Int64 NULL) :: Float64 NULL
5 round(UInt16) :: Float64
6 round(UInt16 NULL) :: Float64 NULL
7 round(UInt16, Int64) :: Float64
8 round(UInt16 NULL, Int64 NULL) :: Float64 NULL
9 round(UInt32) :: Float64
10 round(UInt32 NULL) :: Float64 NULL
11 round(UInt32, Int64) :: Float64
12 round(UInt32 NULL, Int64 NULL) :: Float64 NULL
13 round(UInt64) :: Float64
14 round(UInt64 NULL) :: Float64 NULL
15 round(UInt64, Int64) :: Float64
16 round(UInt64 NULL, Int64 NULL) :: Float64 NULL
17 round(Int8) :: Float64
18 round(Int8 NULL) :: Float64 NULL
19 round(Int8, Int64) :: Float64
20 round(Int8 NULL, Int64 NULL) :: Float64 NULL
21 round(Int16) :: Float64
22 round(Int16 NULL) :: Float64 NULL
23 round(Int16, Int64) :: Float64
24 round(Int16 NULL, Int64 NULL) :: Float64 NULL
25 round(Int32) :: Float64
26 round(Int32 NULL) :: Float64 NULL
27 round(Int32, Int64) :: Float64
28 round(Int32 NULL, Int64 NULL) :: Float64 NULL
29 round(Int64) :: Float64
30 round(Int64 NULL) :: Float64 NULL
31 round(Int64, Int64) :: Float64
32 round(Int64 NULL, Int64 NULL) :: Float64 NULL
33 round(Float32) :: Float64
34 round(Float32 NULL) :: Float64 NULL
35 round(Float32, Int64) :: Float64
36 round(Float32 NULL, Int64 NULL) :: Float64 NULL
37 round(Float64) :: Float64
38 round(Float64 NULL) :: Float64 NULL
39 round(Float64, Int64) :: Float64
40 round(Float64 NULL, Int64 NULL) :: Float64 NULL
0 rpad(String, UInt64, String) :: String
1 rpad(String NULL, UInt64 NULL, String NULL) :: String NULL
0 rtrim(String) :: String
1 rtrim(String NULL) :: String NULL
2 rtrim(String, String) :: String
3 rtrim(String NULL, String NULL) :: String NULL
0 running_difference(Int64) :: Int64
1 running_difference(Int64 NULL) :: Int64 NULL
2 running_difference(Date) :: Int32
3 running_difference(Date NULL) :: Int32 NULL
4 running_difference(Timestamp) :: Int64
5 running_difference(Timestamp NULL) :: Int64 NULL
6 running_difference(Float64) :: Float64
7 running_difference(Float64 NULL) :: Float64 NULL
0 sha(String) :: String
1 sha(String NULL) :: String NULL
0 sha2(String, UInt64) :: String
1 sha2(String NULL, UInt64 NULL) :: String NULL
0 sign(Float64) :: Int8
1 sign(Float64 NULL) :: Int8 NULL
0 sin(Float64) :: Float64
1 sin(Float64 NULL) :: Float64 NULL
0 siphash64(Variant) :: UInt64
1 siphash64(Variant NULL) :: UInt64 NULL
2 siphash64(String) :: UInt64
3 siphash64(String NULL) :: UInt64 NULL
4 siphash64(Date) :: UInt64
5 siphash64(Date NULL) :: UInt64 NULL
6 siphash64(Timestamp) :: UInt64
7 siphash64(Timestamp NULL) :: UInt64 NULL
8 siphash64(Boolean) :: UInt64
9 siphash64(Boolean NULL) :: UInt64 NULL
10 siphash64(Bitmap) :: UInt64
11 siphash64(Bitmap NULL) :: UInt64 NULL
12 siphash64(UInt8) :: UInt64
13 siphash64(UInt8 NULL) :: UInt64 NULL
14 siphash64(Int8) :: UInt64
15 siphash64(Int8 NULL) :: UInt64 NULL
16 siphash64(UInt16) :: UInt64
17 siphash64(UInt16 NULL) :: UInt64 NULL
18 siphash64(Int16) :: UInt64
19 siphash64(Int16 NULL) :: UInt64 NULL
20 siphash64(UInt32) :: UInt64
21 siphash64(UInt32 NULL) :: UInt64 NULL
22 siphash64(Int32) :: UInt64
23 siphash64(Int32 NULL) :: UInt64 NULL
24 siphash64(UInt64) :: UInt64
25 siphash64(UInt64 NULL) :: UInt64 NULL
26 siphash64(Int64) :: UInt64
27 siphash64(Int64 NULL) :: UInt64 NULL
28 siphash64 FACTORY
29 siphash64(Float32) :: UInt64
30 siphash64(Float32 NULL) :: UInt64 NULL
31 siphash64(Float64) :: UInt64
32 siphash64(Float64 NULL) :: UInt64 NULL
33 siphash64(T0) :: UInt64
34 siphash64(T0 NULL) :: UInt64 NULL
0 sleep(Float64) :: UInt8
0 slice(Array(Nothing), UInt64) :: Array(Nothing)
1 slice(Array(Nothing) NULL, UInt64 NULL) :: Array(Nothing) NULL
2 slice(Array(T0), UInt64) :: Array(T0)
3 slice(Array(T0) NULL, UInt64 NULL) :: Array(T0) NULL
4 slice(Array(Nothing), UInt64, UInt64) :: Array(Nothing)
5 slice(Array(Nothing) NULL, UInt64 NULL, UInt64 NULL) :: Array(Nothing) NULL
6 slice(Array(T0), UInt64, UInt64) :: Array(T0)
7 slice(Array(T0) NULL, UInt64 NULL, UInt64 NULL) :: Array(T0) NULL
0 soundex(String) :: String
1 soundex(String NULL) :: String NULL
0 space(UInt64) :: String
1 space(UInt64 NULL) :: String NULL
0 split(String, String) :: Array(String)
1 split(String NULL, String NULL) :: Array(String) NULL
0 split_part(String, String, Int64) :: String
1 split_part(String NULL, String NULL, Int64 NULL) :: String NULL
0 sqrt(UInt8) :: Float64
1 sqrt(UInt8 NULL) :: Float64 NULL
2 sqrt(UInt16) :: Float64
3 sqrt(UInt16 NULL) :: Float64 NULL
4 sqrt(UInt32) :: Float64
5 sqrt(UInt32 NULL) :: Float64 NULL
6 sqrt(UInt64) :: Float64
7 sqrt(UInt64 NULL) :: Float64 NULL
8 sqrt(Int8) :: Float64
9 sqrt(Int8 NULL) :: Float64 NULL
10 sqrt(Int16) :: Float64
11 sqrt(Int16 NULL) :: Float64 NULL
12 sqrt(Int32) :: Float64
13 sqrt(Int32 NULL) :: Float64 NULL
14 sqrt(Int64) :: Float64
15 sqrt(Int64 NULL) :: Float64 NULL
16 sqrt(Float32) :: Float64
17 sqrt(Float32 NULL) :: Float64 NULL
18 sqrt(Float64) :: Float64
19 sqrt(Float64 NULL) :: Float64 NULL
0 st_area(Geometry) :: Float64
1 st_area(Geometry NULL) :: Float64 NULL
0 st_asewkb(Geometry) :: Binary
1 st_asewkb(Geometry NULL) :: Binary NULL
0 st_asewkt(Geometry) :: String
1 st_asewkt(Geometry NULL) :: String NULL
0 st_asgeojson(Geometry) :: Variant
1 st_asgeojson(Geometry NULL) :: Variant NULL
0 st_aswkb(Geometry) :: Binary
1 st_aswkb(Geometry NULL) :: Binary NULL
0 st_aswkt(Geometry) :: String
1 st_aswkt(Geometry NULL) :: String NULL
0 st_contains(Geometry, Geometry) :: Boolean
1 st_contains(Geometry NULL, Geometry NULL) :: Boolean NULL
0 st_convexhull(Geometry) :: Geometry
1 st_convexhull(Geometry NULL) :: Geometry NULL
0 st_dimension(Geometry) :: Int32 NULL
1 st_dimension(Geometry NULL) :: Int32 NULL
0 st_disjoint(Geometry, Geometry) :: Boolean
1 st_disjoint(Geometry NULL, Geometry NULL) :: Boolean NULL
0 st_distance(Geometry, Geometry) :: Float64
1 st_distance(Geometry NULL, Geometry NULL) :: Float64 NULL
0 st_endpoint(Geometry) :: Geometry NULL
1 st_endpoint(Geometry NULL) :: Geometry NULL
0 st_equals(Geometry, Geometry) :: Boolean
1 st_equals(Geometry NULL, Geometry NULL) :: Boolean NULL
0 st_geographyfromewkt(String) :: Geography
1 st_geographyfromewkt(String NULL) :: Geography NULL
0 st_geohash(Geometry) :: String
1 st_geohash(Geometry NULL) :: String NULL
2 st_geohash(Geometry, Int32) :: String
3 st_geohash(Geometry NULL, Int32 NULL) :: String NULL
0 st_geometryfromwkb(String) :: Geometry
1 st_geometryfromwkb(String NULL) :: Geometry NULL
2 st_geometryfromwkb(Binary) :: Geometry
3 st_geometryfromwkb(Binary NULL) :: Geometry NULL
4 st_geometryfromwkb(String, Int32) :: Geometry
5 st_geometryfromwkb(String NULL, Int32 NULL) :: Geometry NULL
6 st_geometryfromwkb(Binary, Int32) :: Geometry
7 st_geometryfromwkb(Binary NULL, Int32 NULL) :: Geometry NULL
0 st_geometryfromwkt(String) :: Geometry
1 st_geometryfromwkt(String NULL) :: Geometry NULL
2 st_geometryfromwkt(String, Int32) :: Geometry
3 st_geometryfromwkt(String NULL, Int32 NULL) :: Geometry NULL
0 st_geomfromgeohash(String) :: Geometry
1 st_geomfromgeohash(String NULL) :: Geometry NULL
0 st_geompointfromgeohash(String) :: Geometry
1 st_geompointfromgeohash(String NULL) :: Geometry NULL
0 st_intersects(Geometry, Geometry) :: Boolean
1 st_intersects(Geometry NULL, Geometry NULL) :: Boolean NULL
0 st_length(Geometry) :: Float64
1 st_length(Geometry NULL) :: Float64 NULL
0 st_makegeompoint(Float64, Float64) :: Geometry
1 st_makegeompoint(Float64 NULL, Float64 NULL) :: Geometry NULL
0 st_makeline(Geometry, Geometry) :: Geometry
1 st_makeline(Geometry NULL, Geometry NULL) :: Geometry NULL
0 st_makepoint(Float64, Float64) :: Geography
1 st_makepoint(Float64 NULL, Float64 NULL) :: Geography NULL
0 st_makepolygon(Geometry) :: Geometry
1 st_makepolygon(Geometry NULL) :: Geometry NULL
0 st_npoints(Geometry) :: UInt32
1 st_npoints(Geometry NULL) :: UInt32 NULL
0 st_pointn(Geometry, Int32) :: Geometry NULL
1 st_pointn(Geometry NULL, Int32 NULL) :: Geometry NULL
0 st_setsrid(Geometry, Int32) :: Geometry
1 st_setsrid(Geometry NULL, Int32 NULL) :: Geometry NULL
0 st_srid(Geometry) :: Int32
1 st_srid(Geometry NULL) :: Int32 NULL
0 st_startpoint(Geometry) :: Geometry NULL
1 st_startpoint(Geometry NULL) :: Geometry NULL
0 st_transform(Geometry, Int32) :: Geometry
1 st_transform(Geometry NULL, Int32 NULL) :: Geometry NULL
2 st_transform(Geometry, Int32, Int32) :: Geometry
3 st_transform(Geometry NULL, Int32 NULL, Int32 NULL) :: Geometry NULL
0 st_within(Geometry, Geometry) :: Boolean
1 st_within(Geometry NULL, Geometry NULL) :: Boolean NULL
0 st_x(Geometry) :: Float64
1 st_x(Geometry NULL) :: Float64 NULL
0 st_xmax(Geometry) :: Float64 NULL
1 st_xmax(Geometry NULL) :: Float64 NULL
0 st_xmin(Geometry) :: Float64 NULL
1 st_xmin(Geometry NULL) :: Float64 NULL
0 st_y(Geometry) :: Float64
1 st_y(Geometry NULL) :: Float64 NULL
0 st_ymax(Geometry) :: Float64 NULL
1 st_ymax(Geometry NULL) :: Float64 NULL
0 st_ymin(Geometry) :: Float64 NULL
1 st_ymin(Geometry NULL) :: Float64 NULL
0 strcmp(String, String) :: Int8
1 strcmp(String NULL, String NULL) :: Int8 NULL
0 string_to_h3(String) :: UInt64
1 string_to_h3(String NULL) :: UInt64 NULL
0 strip_null_value(Variant NULL) :: Variant NULL
0 sub_bitmap(Bitmap, UInt64, UInt64) :: Bitmap
1 sub_bitmap(Bitmap NULL, UInt64 NULL, UInt64 NULL) :: Bitmap NULL
0 substr(String, Int64) :: String
1 substr(String NULL, Int64 NULL) :: String NULL
2 substr(String, Int64, UInt64) :: String
3 substr(String NULL, Int64 NULL, UInt64 NULL) :: String NULL
0 subtract_days(Date, Int64) :: Date
1 subtract_days(Date NULL, Int64 NULL) :: Date NULL
2 subtract_days(Timestamp, Int64) :: Timestamp
3 subtract_days(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 subtract_hours(Date, Int64) :: Timestamp
1 subtract_hours(Date NULL, Int64 NULL) :: Timestamp NULL
2 subtract_hours(Timestamp, Int64) :: Timestamp
3 subtract_hours(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 subtract_minutes(Date, Int64) :: Timestamp
1 subtract_minutes(Date NULL, Int64 NULL) :: Timestamp NULL
2 subtract_minutes(Timestamp, Int64) :: Timestamp
3 subtract_minutes(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 subtract_months(Date, Int64) :: Date
1 subtract_months(Date NULL, Int64 NULL) :: Date NULL
2 subtract_months(Timestamp, Int64) :: Timestamp
3 subtract_months(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 subtract_quarters(Date, Int64) :: Date
1 subtract_quarters(Date NULL, Int64 NULL) :: Date NULL
2 subtract_quarters(Timestamp, Int64) :: Timestamp
3 subtract_quarters(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 subtract_seconds(Date, Int64) :: Timestamp
1 subtract_seconds(Date NULL, Int64 NULL) :: Timestamp NULL
2 subtract_seconds(Timestamp, Int64) :: Timestamp
3 subtract_seconds(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 subtract_weeks(Date, Int64) :: Date
1 subtract_weeks(Date NULL, Int64 NULL) :: Date NULL
2 subtract_weeks(Timestamp, Int64) :: Timestamp
3 subtract_weeks(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 subtract_years(Date, Int64) :: Date
1 subtract_years(Date NULL, Int64 NULL) :: Date NULL
2 subtract_years(Timestamp, Int64) :: Timestamp
3 subtract_years(Timestamp NULL, Int64 NULL) :: Timestamp NULL
0 tan(Float64) :: Float64
1 tan(Float64 NULL) :: Float64 NULL
0 time_slot(Timestamp) :: Timestamp
1 time_slot(Timestamp NULL) :: Timestamp NULL
0 timestamp_diff(Timestamp, Timestamp) :: Interval
1 timestamp_diff(Timestamp NULL, Timestamp NULL) :: Interval NULL
0 to_base64(Binary) :: String
1 to_base64(Binary NULL) :: String NULL
0 to_binary(Variant) :: Binary NULL
1 to_binary(Variant NULL) :: Binary NULL
2 to_binary(Bitmap) :: Binary
3 to_binary(Bitmap NULL) :: Binary NULL
4 to_binary(Geometry) :: Binary
5 to_binary(Geometry NULL) :: Binary NULL
6 to_binary(Geography) :: Binary
7 to_binary(Geography NULL) :: Binary NULL
8 to_binary(String) :: Binary
9 to_binary(String NULL) :: Binary NULL
0 to_bitmap(String) :: Bitmap
1 to_bitmap(String NULL) :: Bitmap NULL
2 to_bitmap(UInt64) :: Bitmap
3 to_bitmap(UInt64 NULL) :: Bitmap NULL
0 to_boolean(Variant) :: Boolean NULL
1 to_boolean(Variant NULL) :: Boolean NULL
2 to_boolean(String) :: Boolean
3 to_boolean(String NULL) :: Boolean NULL
4 to_boolean(UInt8) :: Boolean
5 to_boolean(UInt8 NULL) :: Boolean NULL
6 to_boolean(UInt16) :: Boolean
7 to_boolean(UInt16 NULL) :: Boolean NULL
8 to_boolean(UInt32) :: Boolean
9 to_boolean(UInt32 NULL) :: Boolean NULL
10 to_boolean(UInt64) :: Boolean
11 to_boolean(UInt64 NULL) :: Boolean NULL
12 to_boolean(Int8) :: Boolean
13 to_boolean(Int8 NULL) :: Boolean NULL
14 to_boolean(Int16) :: Boolean
15 to_boolean(Int16 NULL) :: Boolean NULL
16 to_boolean(Int32) :: Boolean
17 to_boolean(Int32 NULL) :: Boolean NULL
18 to_boolean(Int64) :: Boolean
19 to_boolean(Int64 NULL) :: Boolean NULL
20 to_boolean(Float32) :: Boolean
21 to_boolean(Float32 NULL) :: Boolean NULL
22 to_boolean(Float64) :: Boolean
23 to_boolean(Float64 NULL) :: Boolean NULL
0 to_centuries(Int64) :: Interval
1 to_centuries(Int64 NULL) :: Interval NULL
0 to_date(Variant) :: Date NULL
1 to_date(Variant NULL) :: Date NULL
2 to_date(String, String) :: Date NULL
3 to_date(String NULL, String NULL) :: Date NULL
4 to_date(String) :: Date
5 to_date(String NULL) :: Date NULL
6 to_date(Timestamp) :: Date
7 to_date(Timestamp NULL) :: Date NULL
8 to_date(Int64) :: Date
9 to_date(Int64 NULL) :: Date NULL
0 to_day_of_month(Date) :: UInt8
1 to_day_of_month(Date NULL) :: UInt8 NULL
2 to_day_of_month(Timestamp) :: UInt8
3 to_day_of_month(Timestamp NULL) :: UInt8 NULL
4 to_day_of_month(Interval) :: Int64
5 to_day_of_month(Interval NULL) :: Int64 NULL
0 to_day_of_week(Date) :: UInt8
1 to_day_of_week(Date NULL) :: UInt8 NULL
2 to_day_of_week(Timestamp) :: UInt8
3 to_day_of_week(Timestamp NULL) :: UInt8 NULL
0 to_day_of_year(Date) :: UInt16
1 to_day_of_year(Date NULL) :: UInt16 NULL
2 to_day_of_year(Timestamp) :: UInt16
3 to_day_of_year(Timestamp NULL) :: UInt16 NULL
0 to_days(Int64) :: Interval
1 to_days(Int64 NULL) :: Interval NULL
0 to_decades(Int64) :: Interval
1 to_decades(Int64 NULL) :: Interval NULL
0 to_decimal FACTORY
1 to_decimal FACTORY
0 to_float32(Variant) :: Float32 NULL
1 to_float32(Variant NULL) :: Float32 NULL
2 to_float32(String) :: Float32
3 to_float32(String NULL) :: Float32 NULL
4 to_float32(UInt8) :: Float32
5 to_float32(UInt8 NULL) :: Float32 NULL
6 to_float32(Int8) :: Float32
7 to_float32(Int8 NULL) :: Float32 NULL
8 to_float32(UInt16) :: Float32
9 to_float32(UInt16 NULL) :: Float32 NULL
10 to_float32(Int16) :: Float32
11 to_float32(Int16 NULL) :: Float32 NULL
12 to_float32(UInt32) :: Float32
13 to_float32(UInt32 NULL) :: Float32 NULL
14 to_float32(Int32) :: Float32
15 to_float32(Int32 NULL) :: Float32 NULL
16 to_float32(UInt64) :: Float32
17 to_float32(UInt64 NULL) :: Float32 NULL
18 to_float32(Int64) :: Float32
19 to_float32(Int64 NULL) :: Float32 NULL
20 to_float32 FACTORY
21 to_float32 FACTORY
22 to_float32(Float64) :: Float32
23 to_float32(Float64 NULL) :: Float32 NULL
24 to_float32(Boolean) :: Float32
25 to_float32(Boolean NULL) :: Float32 NULL
0 to_float64(Variant) :: Float64 NULL
1 to_float64(Variant NULL) :: Float64 NULL
2 to_float64(String) :: Float64
3 to_float64(String NULL) :: Float64 NULL
4 to_float64(UInt8) :: Float64
5 to_float64(UInt8 NULL) :: Float64 NULL
6 to_float64(Int8) :: Float64
7 to_float64(Int8 NULL) :: Float64 NULL
8 to_float64(UInt16) :: Float64
9 to_float64(UInt16 NULL) :: Float64 NULL
10 to_float64(Int16) :: Float64
11 to_float64(Int16 NULL) :: Float64 NULL
12 to_float64(UInt32) :: Float64
13 to_float64(UInt32 NULL) :: Float64 NULL
14 to_float64(Int32) :: Float64
15 to_float64(Int32 NULL) :: Float64 NULL
16 to_float64(UInt64) :: Float64
17 to_float64(UInt64 NULL) :: Float64 NULL
18 to_float64(Int64) :: Float64
19 to_float64(Int64 NULL) :: Float64 NULL
20 to_float64 FACTORY
21 to_float64 FACTORY
22 to_float64(Float32) :: Float64
23 to_float64(Float32 NULL) :: Float64 NULL
24 to_float64(Boolean) :: Float64
25 to_float64(Boolean NULL) :: Float64 NULL
0 to_geometry(String) :: Geometry
1 to_geometry(String NULL) :: Geometry NULL
2 to_geometry(String, Int32) :: Geometry
3 to_geometry(String NULL, Int32 NULL) :: Geometry NULL
4 to_geometry(Binary) :: Geometry
5 to_geometry(Binary NULL) :: Geometry NULL
6 to_geometry(Binary, Int32) :: Geometry
7 to_geometry(Binary NULL, Int32 NULL) :: Geometry NULL
8 to_geometry(Variant) :: Geometry
9 to_geometry(Variant NULL) :: Geometry NULL
10 to_geometry(Variant, Int32) :: Geometry
11 to_geometry(Variant NULL, Int32 NULL) :: Geometry NULL
0 to_hex(String) :: String
1 to_hex(String NULL) :: String NULL
2 to_hex(Int64) :: String
3 to_hex(Int64 NULL) :: String NULL
4 to_hex(Binary) :: String
5 to_hex(Binary NULL) :: String NULL
0 to_hour(Timestamp) :: UInt8
1 to_hour(Timestamp NULL) :: UInt8 NULL
2 to_hour(Interval) :: Int64
3 to_hour(Interval NULL) :: Int64 NULL
0 to_hours(Int64) :: Interval
1 to_hours(Int64 NULL) :: Interval NULL
0 to_int16(Variant) :: Int16 NULL
1 to_int16(Variant NULL) :: Int16 NULL
2 to_int16(String) :: Int16
3 to_int16(String NULL) :: Int16 NULL
4 to_int16(UInt8) :: Int16
5 to_int16(UInt8 NULL) :: Int16 NULL
6 to_int16(Int8) :: Int16
7 to_int16(Int8 NULL) :: Int16 NULL
8 to_int16(UInt16) :: Int16
9 to_int16(UInt16 NULL) :: Int16 NULL
10 to_int16(UInt32) :: Int16
11 to_int16(UInt32 NULL) :: Int16 NULL
12 to_int16(Int32) :: Int16
13 to_int16(Int32 NULL) :: Int16 NULL
14 to_int16(UInt64) :: Int16
15 to_int16(UInt64 NULL) :: Int16 NULL
16 to_int16(Int64) :: Int16
17 to_int16(Int64 NULL) :: Int16 NULL
18 to_int16 FACTORY
19 to_int16 FACTORY
20 to_int16(Float32) :: Int16
21 to_int16(Float32 NULL) :: Int16 NULL
22 to_int16(Float64) :: Int16
23 to_int16(Float64 NULL) :: Int16 NULL
24 to_int16(Boolean) :: Int16
25 to_int16(Boolean NULL) :: Int16 NULL
0 to_int32(Variant) :: Int32 NULL
1 to_int32(Variant NULL) :: Int32 NULL
2 to_int32(String) :: Int32
3 to_int32(String NULL) :: Int32 NULL
4 to_int32(UInt8) :: Int32
5 to_int32(UInt8 NULL) :: Int32 NULL
6 to_int32(Int8) :: Int32
7 to_int32(Int8 NULL) :: Int32 NULL
8 to_int32(UInt16) :: Int32
9 to_int32(UInt16 NULL) :: Int32 NULL
10 to_int32(Int16) :: Int32
11 to_int32(Int16 NULL) :: Int32 NULL
12 to_int32(UInt32) :: Int32
13 to_int32(UInt32 NULL) :: Int32 NULL
14 to_int32(UInt64) :: Int32
15 to_int32(UInt64 NULL) :: Int32 NULL
16 to_int32(Int64) :: Int32
17 to_int32(Int64 NULL) :: Int32 NULL
18 to_int32 FACTORY
19 to_int32 FACTORY
20 to_int32(Float32) :: Int32
21 to_int32(Float32 NULL) :: Int32 NULL
22 to_int32(Float64) :: Int32
23 to_int32(Float64 NULL) :: Int32 NULL
24 to_int32(Boolean) :: Int32
25 to_int32(Boolean NULL) :: Int32 NULL
0 to_int64(Variant) :: Int64 NULL
1 to_int64(Variant NULL) :: Int64 NULL
2 to_int64(String) :: Int64
3 to_int64(String NULL) :: Int64 NULL
4 to_int64(UInt8) :: Int64
5 to_int64(UInt8 NULL) :: Int64 NULL
6 to_int64(Int8) :: Int64
7 to_int64(Int8 NULL) :: Int64 NULL
8 to_int64(UInt16) :: Int64
9 to_int64(UInt16 NULL) :: Int64 NULL
10 to_int64(Int16) :: Int64
11 to_int64(Int16 NULL) :: Int64 NULL
12 to_int64(UInt32) :: Int64
13 to_int64(UInt32 NULL) :: Int64 NULL
14 to_int64(Int32) :: Int64
15 to_int64(Int32 NULL) :: Int64 NULL
16 to_int64(UInt64) :: Int64
17 to_int64(UInt64 NULL) :: Int64 NULL
18 to_int64 FACTORY
19 to_int64 FACTORY
20 to_int64(Float32) :: Int64
21 to_int64(Float32 NULL) :: Int64 NULL
22 to_int64(Float64) :: Int64
23 to_int64(Float64 NULL) :: Int64 NULL
24 to_int64(Boolean) :: Int64
25 to_int64(Boolean NULL) :: Int64 NULL
26 to_int64(Date) :: Int64
27 to_int64(Date NULL) :: Int64 NULL
28 to_int64(Timestamp) :: Int64
29 to_int64(Timestamp NULL) :: Int64 NULL
0 to_int8(Variant) :: Int8 NULL
1 to_int8(Variant NULL) :: Int8 NULL
2 to_int8(String) :: Int8
3 to_int8(String NULL) :: Int8 NULL
4 to_int8(UInt8) :: Int8
5 to_int8(UInt8 NULL) :: Int8 NULL
6 to_int8(UInt16) :: Int8
7 to_int8(UInt16 NULL) :: Int8 NULL
8 to_int8(Int16) :: Int8
9 to_int8(Int16 NULL) :: Int8 NULL
10 to_int8(UInt32) :: Int8
11 to_int8(UInt32 NULL) :: Int8 NULL
12 to_int8(Int32) :: Int8
13 to_int8(Int32 NULL) :: Int8 NULL
14 to_int8(UInt64) :: Int8
15 to_int8(UInt64 NULL) :: Int8 NULL
16 to_int8(Int64) :: Int8
17 to_int8(Int64 NULL) :: Int8 NULL
18 to_int8 FACTORY
19 to_int8 FACTORY
20 to_int8(Float32) :: Int8
21 to_int8(Float32 NULL) :: Int8 NULL
22 to_int8(Float64) :: Int8
23 to_int8(Float64 NULL) :: Int8 NULL
24 to_int8(Boolean) :: Int8
25 to_int8(Boolean NULL) :: Int8 NULL
0 to_interval(Variant) :: Interval NULL
1 to_interval(Variant NULL) :: Interval NULL
2 to_interval(String) :: Interval
3 to_interval(String NULL) :: Interval NULL
0 to_iso_year(Date) :: UInt16
1 to_iso_year(Date NULL) :: UInt16 NULL
2 to_iso_year(Timestamp) :: UInt16
3 to_iso_year(Timestamp NULL) :: UInt16 NULL
0 to_jsonb_binary(Variant) :: Binary
1 to_jsonb_binary(Variant NULL) :: Binary NULL
0 to_last_of_month(Date) :: Date
1 to_last_of_month(Date NULL) :: Date NULL
2 to_last_of_month(Timestamp) :: Date
3 to_last_of_month(Timestamp NULL) :: Date NULL
0 to_last_of_quarter(Date) :: Date
1 to_last_of_quarter(Date NULL) :: Date NULL
2 to_last_of_quarter(Timestamp) :: Date
3 to_last_of_quarter(Timestamp NULL) :: Date NULL
0 to_last_of_week(Date) :: Date
1 to_last_of_week(Date NULL) :: Date NULL
2 to_last_of_week(Timestamp) :: Date
3 to_last_of_week(Timestamp NULL) :: Date NULL
0 to_last_of_year(Date) :: Date
1 to_last_of_year(Date NULL) :: Date NULL
2 to_last_of_year(Timestamp) :: Date
3 to_last_of_year(Timestamp NULL) :: Date NULL
0 to_microsecond(Interval) :: Int64
1 to_microsecond(Interval NULL) :: Int64 NULL
0 to_microseconds(Int64) :: Interval
1 to_microseconds(Int64 NULL) :: Interval NULL
0 to_millennia(Int64) :: Interval
1 to_millennia(Int64 NULL) :: Interval NULL
0 to_milliseconds(Int64) :: Interval
1 to_milliseconds(Int64 NULL) :: Interval NULL
0 to_minute(Timestamp) :: UInt8
1 to_minute(Timestamp NULL) :: UInt8 NULL
2 to_minute(Interval) :: Int64
3 to_minute(Interval NULL) :: Int64 NULL
0 to_minutes(Int64) :: Interval
1 to_minutes(Int64 NULL) :: Interval NULL
0 to_monday(Date) :: Date
1 to_monday(Date NULL) :: Date NULL
2 to_monday(Timestamp) :: Date
3 to_monday(Timestamp NULL) :: Date NULL
0 to_month(Date) :: UInt8
1 to_month(Date NULL) :: UInt8 NULL
2 to_month(Timestamp) :: UInt8
3 to_month(Timestamp NULL) :: UInt8 NULL
4 to_month(Interval) :: Int64
5 to_month(Interval NULL) :: Int64 NULL
0 to_months(Int64) :: Interval
1 to_months(Int64 NULL) :: Interval NULL
0 to_next_friday(Date) :: Date
1 to_next_friday(Date NULL) :: Date NULL
2 to_next_friday(Timestamp) :: Date
3 to_next_friday(Timestamp NULL) :: Date NULL
0 to_next_monday(Date) :: Date
1 to_next_monday(Date NULL) :: Date NULL
2 to_next_monday(Timestamp) :: Date
3 to_next_monday(Timestamp NULL) :: Date NULL
0 to_next_saturday(Date) :: Date
1 to_next_saturday(Date NULL) :: Date NULL
2 to_next_saturday(Timestamp) :: Date
3 to_next_saturday(Timestamp NULL) :: Date NULL
0 to_next_sunday(Date) :: Date
1 to_next_sunday(Date NULL) :: Date NULL
2 to_next_sunday(Timestamp) :: Date
3 to_next_sunday(Timestamp NULL) :: Date NULL
0 to_next_thursday(Date) :: Date
1 to_next_thursday(Date NULL) :: Date NULL
2 to_next_thursday(Timestamp) :: Date
3 to_next_thursday(Timestamp NULL) :: Date NULL
0 to_next_tuesday(Date) :: Date
1 to_next_tuesday(Date NULL) :: Date NULL
2 to_next_tuesday(Timestamp) :: Date
3 to_next_tuesday(Timestamp NULL) :: Date NULL
0 to_next_wednesday(Date) :: Date
1 to_next_wednesday(Date NULL) :: Date NULL
2 to_next_wednesday(Timestamp) :: Date
3 to_next_wednesday(Timestamp NULL) :: Date NULL
0 to_nullable(NULL) :: NULL
1 to_nullable(T0 NULL) :: T0 NULL
0 to_previous_friday(Date) :: Date
1 to_previous_friday(Date NULL) :: Date NULL
2 to_previous_friday(Timestamp) :: Date
3 to_previous_friday(Timestamp NULL) :: Date NULL
0 to_previous_monday(Date) :: Date
1 to_previous_monday(Date NULL) :: Date NULL
2 to_previous_monday(Timestamp) :: Date
3 to_previous_monday(Timestamp NULL) :: Date NULL
0 to_previous_saturday(Date) :: Date
1 to_previous_saturday(Date NULL) :: Date NULL
2 to_previous_saturday(Timestamp) :: Date
3 to_previous_saturday(Timestamp NULL) :: Date NULL
0 to_previous_sunday(Date) :: Date
1 to_previous_sunday(Date NULL) :: Date NULL
2 to_previous_sunday(Timestamp) :: Date
3 to_previous_sunday(Timestamp NULL) :: Date NULL
0 to_previous_thursday(Date) :: Date
1 to_previous_thursday(Date NULL) :: Date NULL
2 to_previous_thursday(Timestamp) :: Date
3 to_previous_thursday(Timestamp NULL) :: Date NULL
0 to_previous_tuesday(Date) :: Date
1 to_previous_tuesday(Date NULL) :: Date NULL
2 to_previous_tuesday(Timestamp) :: Date
3 to_previous_tuesday(Timestamp NULL) :: Date NULL
0 to_previous_wednesday(Date) :: Date
1 to_previous_wednesday(Date NULL) :: Date NULL
2 to_previous_wednesday(Timestamp) :: Date
3 to_previous_wednesday(Timestamp NULL) :: Date NULL
0 to_quarter(Date) :: UInt8
1 to_quarter(Date NULL) :: UInt8 NULL
2 to_quarter(Timestamp) :: UInt8
3 to_quarter(Timestamp NULL) :: UInt8 NULL
0 to_second(Timestamp) :: UInt8
1 to_second(Timestamp NULL) :: UInt8 NULL
2 to_second(Interval) :: Float64
3 to_second(Interval NULL) :: Float64 NULL
0 to_seconds(Int64) :: Interval
1 to_seconds(Int64 NULL) :: Interval NULL
0 to_start_of_day(Timestamp) :: Timestamp
1 to_start_of_day(Timestamp NULL) :: Timestamp NULL
0 to_start_of_fifteen_minutes(Timestamp) :: Timestamp
1 to_start_of_fifteen_minutes(Timestamp NULL) :: Timestamp NULL
0 to_start_of_five_minutes(Timestamp) :: Timestamp
1 to_start_of_five_minutes(Timestamp NULL) :: Timestamp NULL
0 to_start_of_hour(Timestamp) :: Timestamp
1 to_start_of_hour(Timestamp NULL) :: Timestamp NULL
0 to_start_of_iso_year(Date) :: Date
1 to_start_of_iso_year(Date NULL) :: Date NULL
2 to_start_of_iso_year(Timestamp) :: Date
3 to_start_of_iso_year(Timestamp NULL) :: Date NULL
0 to_start_of_minute(Timestamp) :: Timestamp
1 to_start_of_minute(Timestamp NULL) :: Timestamp NULL
0 to_start_of_month(Date) :: Date
1 to_start_of_month(Date NULL) :: Date NULL
2 to_start_of_month(Timestamp) :: Date
3 to_start_of_month(Timestamp NULL) :: Date NULL
0 to_start_of_quarter(Date) :: Date
1 to_start_of_quarter(Date NULL) :: Date NULL
2 to_start_of_quarter(Timestamp) :: Date
3 to_start_of_quarter(Timestamp NULL) :: Date NULL
0 to_start_of_second(Timestamp) :: Timestamp
1 to_start_of_second(Timestamp NULL) :: Timestamp NULL
0 to_start_of_ten_minutes(Timestamp) :: Timestamp
1 to_start_of_ten_minutes(Timestamp NULL) :: Timestamp NULL
0 to_start_of_week(Date) :: Date
1 to_start_of_week(Date NULL) :: Date NULL
2 to_start_of_week(Timestamp) :: Date
3 to_start_of_week(Timestamp NULL) :: Date NULL
4 to_start_of_week(Date, Int64) :: Date
5 to_start_of_week(Date NULL, Int64 NULL) :: Date NULL
6 to_start_of_week(Timestamp, Int64) :: Date
7 to_start_of_week(Timestamp NULL, Int64 NULL) :: Date NULL
0 to_start_of_year(Date) :: Date
1 to_start_of_year(Date NULL) :: Date NULL
2 to_start_of_year(Timestamp) :: Date
3 to_start_of_year(Timestamp NULL) :: Date NULL
0 to_string(Variant) :: String NULL
1 to_string(Variant NULL) :: String NULL
2 to_string(UInt8) :: String
3 to_string(UInt8 NULL) :: String NULL
4 to_string(Int8) :: String
5 to_string(Int8 NULL) :: String NULL
6 to_string(UInt16) :: String
7 to_string(UInt16 NULL) :: String NULL
8 to_string(Int16) :: String
9 to_string(Int16 NULL) :: String NULL
10 to_string(UInt32) :: String
11 to_string(UInt32 NULL) :: String NULL
12 to_string(Int32) :: String
13 to_string(Int32 NULL) :: String NULL
14 to_string(UInt64) :: String
15 to_string(UInt64 NULL) :: String NULL
16 to_string(Int64) :: String
17 to_string(Int64 NULL) :: String NULL
18 to_string FACTORY
19 to_string(Float32) :: String
20 to_string(Float32 NULL) :: String NULL
21 to_string(Float64) :: String
22 to_string(Float64 NULL) :: String NULL
23 to_string(Boolean) :: String
24 to_string(Boolean NULL) :: String NULL
25 to_string(Timestamp, String) :: String NULL
26 to_string(Timestamp NULL, String NULL) :: String NULL
27 to_string(Date) :: String
28 to_string(Date NULL) :: String NULL
29 to_string(Timestamp) :: String
30 to_string(Timestamp NULL) :: String NULL
31 to_string(Binary) :: String
32 to_string(Binary NULL) :: String NULL
33 to_string(Int64, String) :: String
34 to_string(Int64 NULL, String NULL) :: String NULL
35 to_string(Float32, String) :: String
36 to_string(Float32 NULL, String NULL) :: String NULL
37 to_string(Float64, String) :: String
38 to_string(Float64 NULL, String NULL) :: String NULL
39 to_string(Bitmap) :: String
40 to_string(Bitmap NULL) :: String NULL
41 to_string(Geometry) :: String
42 to_string(Geometry NULL) :: String NULL
43 to_string(Interval) :: String
44 to_string(Interval NULL) :: String NULL
0 to_timestamp(Variant) :: Timestamp NULL
1 to_timestamp(Variant NULL) :: Timestamp NULL
2 to_timestamp(String) :: Timestamp
3 to_timestamp(String NULL) :: Timestamp NULL
4 to_timestamp(String, String) :: Timestamp NULL
5 to_timestamp(String NULL, String NULL) :: Timestamp NULL
6 to_timestamp(Date) :: Timestamp
7 to_timestamp(Date NULL) :: Timestamp NULL
8 to_timestamp(Int64) :: Timestamp
9 to_timestamp(Int64 NULL) :: Timestamp NULL
10 to_timestamp(Int64, UInt64) :: Timestamp
11 to_timestamp(Int64 NULL, UInt64 NULL) :: Timestamp NULL
0 to_uint16(Variant) :: UInt16 NULL
1 to_uint16(Variant NULL) :: UInt16 NULL
2 to_uint16(String) :: UInt16
3 to_uint16(String NULL) :: UInt16 NULL
4 to_uint16(UInt8) :: UInt16
5 to_uint16(UInt8 NULL) :: UInt16 NULL
6 to_uint16(Int8) :: UInt16
7 to_uint16(Int8 NULL) :: UInt16 NULL
8 to_uint16(Int16) :: UInt16
9 to_uint16(Int16 NULL) :: UInt16 NULL
10 to_uint16(UInt32) :: UInt16
11 to_uint16(UInt32 NULL) :: UInt16 NULL
12 to_uint16(Int32) :: UInt16
13 to_uint16(Int32 NULL) :: UInt16 NULL
14 to_uint16(UInt64) :: UInt16
15 to_uint16(UInt64 NULL) :: UInt16 NULL
16 to_uint16(Int64) :: UInt16
17 to_uint16(Int64 NULL) :: UInt16 NULL
18 to_uint16 FACTORY
19 to_uint16 FACTORY
20 to_uint16(Float32) :: UInt16
21 to_uint16(Float32 NULL) :: UInt16 NULL
22 to_uint16(Float64) :: UInt16
23 to_uint16(Float64 NULL) :: UInt16 NULL
24 to_uint16(Boolean) :: UInt16
25 to_uint16(Boolean NULL) :: UInt16 NULL
0 to_uint32(Variant) :: UInt32 NULL
1 to_uint32(Variant NULL) :: UInt32 NULL
2 to_uint32(String) :: UInt32
3 to_uint32(String NULL) :: UInt32 NULL
4 to_uint32(UInt8) :: UInt32
5 to_uint32(UInt8 NULL) :: UInt32 NULL
6 to_uint32(Int8) :: UInt32
7 to_uint32(Int8 NULL) :: UInt32 NULL
8 to_uint32(UInt16) :: UInt32
9 to_uint32(UInt16 NULL) :: UInt32 NULL
10 to_uint32(Int16) :: UInt32
11 to_uint32(Int16 NULL) :: UInt32 NULL
12 to_uint32(Int32) :: UInt32
13 to_uint32(Int32 NULL) :: UInt32 NULL
14 to_uint32(UInt64) :: UInt32
15 to_uint32(UInt64 NULL) :: UInt32 NULL
16 to_uint32(Int64) :: UInt32
17 to_uint32(Int64 NULL) :: UInt32 NULL
18 to_uint32 FACTORY
19 to_uint32 FACTORY
20 to_uint32(Float32) :: UInt32
21 to_uint32(Float32 NULL) :: UInt32 NULL
22 to_uint32(Float64) :: UInt32
23 to_uint32(Float64 NULL) :: UInt32 NULL
24 to_uint32(Boolean) :: UInt32
25 to_uint32(Boolean NULL) :: UInt32 NULL
0 to_uint64(Variant) :: UInt64 NULL
1 to_uint64(Variant NULL) :: UInt64 NULL
2 to_uint64(String) :: UInt64
3 to_uint64(String NULL) :: UInt64 NULL
4 to_uint64(UInt8) :: UInt64
5 to_uint64(UInt8 NULL) :: UInt64 NULL
6 to_uint64(Int8) :: UInt64
7 to_uint64(Int8 NULL) :: UInt64 NULL
8 to_uint64(UInt16) :: UInt64
9 to_uint64(UInt16 NULL) :: UInt64 NULL
10 to_uint64(Int16) :: UInt64
11 to_uint64(Int16 NULL) :: UInt64 NULL
12 to_uint64(UInt32) :: UInt64
13 to_uint64(UInt32 NULL) :: UInt64 NULL
14 to_uint64(Int32) :: UInt64
15 to_uint64(Int32 NULL) :: UInt64 NULL
16 to_uint64(Int64) :: UInt64
17 to_uint64(Int64 NULL) :: UInt64 NULL
18 to_uint64 FACTORY
19 to_uint64 FACTORY
20 to_uint64(Float32) :: UInt64
21 to_uint64(Float32 NULL) :: UInt64 NULL
22 to_uint64(Float64) :: UInt64
23 to_uint64(Float64 NULL) :: UInt64 NULL
24 to_uint64(Boolean) :: UInt64
25 to_uint64(Boolean NULL) :: UInt64 NULL
0 to_uint8(Variant) :: UInt8 NULL
1 to_uint8(Variant NULL) :: UInt8 NULL
2 to_uint8(String) :: UInt8
3 to_uint8(String NULL) :: UInt8 NULL
4 to_uint8(Int8) :: UInt8
5 to_uint8(Int8 NULL) :: UInt8 NULL
6 to_uint8(UInt16) :: UInt8
7 to_uint8(UInt16 NULL) :: UInt8 NULL
8 to_uint8(Int16) :: UInt8
9 to_uint8(Int16 NULL) :: UInt8 NULL
10 to_uint8(UInt32) :: UInt8
11 to_uint8(UInt32 NULL) :: UInt8 NULL
12 to_uint8(Int32) :: UInt8
13 to_uint8(Int32 NULL) :: UInt8 NULL
14 to_uint8(UInt64) :: UInt8
15 to_uint8(UInt64 NULL) :: UInt8 NULL
16 to_uint8(Int64) :: UInt8
17 to_uint8(Int64 NULL) :: UInt8 NULL
18 to_uint8 FACTORY
19 to_uint8 FACTORY
20 to_uint8(Float32) :: UInt8
21 to_uint8(Float32 NULL) :: UInt8 NULL
22 to_uint8(Float64) :: UInt8
23 to_uint8(Float64 NULL) :: UInt8 NULL
24 to_uint8(Boolean) :: UInt8
25 to_uint8(Boolean NULL) :: UInt8 NULL
0 to_unix_timestamp(Timestamp) :: Int64
1 to_unix_timestamp(Timestamp NULL) :: Int64 NULL
0 to_uuid FunctionFactoryHelper { fixed_arg_count: Some(1), passthrough_nullable: true }
0 to_variant FACTORY
0 to_week_of_year(Date) :: UInt32
1 to_week_of_year(Date NULL) :: UInt32 NULL
2 to_week_of_year(Timestamp) :: UInt32
3 to_week_of_year(Timestamp NULL) :: UInt32 NULL
0 to_weeks(Int64) :: Interval
1 to_weeks(Int64 NULL) :: Interval NULL
0 to_year(Date) :: UInt16
1 to_year(Date NULL) :: UInt16 NULL
2 to_year(Timestamp) :: UInt16
3 to_year(Timestamp NULL) :: UInt16 NULL
4 to_year(Interval) :: Int64
5 to_year(Interval NULL) :: Int64 NULL
0 to_years(Int64) :: Interval
1 to_years(Int64 NULL) :: Interval NULL
0 to_yyyymm(Date) :: UInt32
1 to_yyyymm(Date NULL) :: UInt32 NULL
2 to_yyyymm(Timestamp) :: UInt32
3 to_yyyymm(Timestamp NULL) :: UInt32 NULL
0 to_yyyymmdd(Date) :: UInt32
1 to_yyyymmdd(Date NULL) :: UInt32 NULL
2 to_yyyymmdd(Timestamp) :: UInt32
3 to_yyyymmdd(Timestamp NULL) :: UInt32 NULL
0 to_yyyymmddhh(Timestamp) :: UInt64
1 to_yyyymmddhh(Timestamp NULL) :: UInt64 NULL
0 to_yyyymmddhhmmss(Timestamp) :: UInt64
1 to_yyyymmddhhmmss(Timestamp NULL) :: UInt64 NULL
0 today() :: Date
0 tomorrow() :: Date
0 translate(String, String, String) :: String
1 translate(String NULL, String NULL, String NULL) :: String NULL
0 trim(String) :: String
1 trim(String NULL) :: String NULL
2 trim(String, String) :: String
3 trim(String NULL, String NULL) :: String NULL
0 trim_both(String, String) :: String
1 trim_both(String NULL, String NULL) :: String NULL
0 trim_leading(String, String) :: String
1 trim_leading(String NULL, String NULL) :: String NULL
0 trim_trailing(String, String) :: String
1 trim_trailing(String NULL, String NULL) :: String NULL
0 truncate FACTORY
1 truncate(UInt8) :: Float64
2 truncate(UInt8 NULL) :: Float64 NULL
3 truncate(UInt8, Int64) :: Float64
4 truncate(UInt8 NULL, Int64 NULL) :: Float64 NULL
5 truncate(UInt16) :: Float64
6 truncate(UInt16 NULL) :: Float64 NULL
7 truncate(UInt16, Int64) :: Float64
8 truncate(UInt16 NULL, Int64 NULL) :: Float64 NULL
9 truncate(UInt32) :: Float64
10 truncate(UInt32 NULL) :: Float64 NULL
11 truncate(UInt32, Int64) :: Float64
12 truncate(UInt32 NULL, Int64 NULL) :: Float64 NULL
13 truncate(UInt64) :: Float64
14 truncate(UInt64 NULL) :: Float64 NULL
15 truncate(UInt64, Int64) :: Float64
16 truncate(UInt64 NULL, Int64 NULL) :: Float64 NULL
17 truncate(Int8) :: Float64
18 truncate(Int8 NULL) :: Float64 NULL
19 truncate(Int8, Int64) :: Float64
20 truncate(Int8 NULL, Int64 NULL) :: Float64 NULL
21 truncate(Int16) :: Float64
22 truncate(Int16 NULL) :: Float64 NULL
23 truncate(Int16, Int64) :: Float64
24 truncate(Int16 NULL, Int64 NULL) :: Float64 NULL
25 truncate(Int32) :: Float64
26 truncate(Int32 NULL) :: Float64 NULL
27 truncate(Int32, Int64) :: Float64
28 truncate(Int32 NULL, Int64 NULL) :: Float64 NULL
29 truncate(Int64) :: Float64
30 truncate(Int64 NULL) :: Float64 NULL
31 truncate(Int64, Int64) :: Float64
32 truncate(Int64 NULL, Int64 NULL) :: Float64 NULL
33 truncate(Float32) :: Float64
34 truncate(Float32 NULL) :: Float64 NULL
35 truncate(Float32, Int64) :: Float64
36 truncate(Float32 NULL, Int64 NULL) :: Float64 NULL
37 truncate(Float64) :: Float64
38 truncate(Float64 NULL) :: Float64 NULL
39 truncate(Float64, Int64) :: Float64
40 truncate(Float64 NULL, Int64 NULL) :: Float64 NULL
0 try_from_base64(String) :: Binary NULL
1 try_from_base64(String NULL) :: Binary NULL
0 try_from_hex(String) :: Binary NULL
1 try_from_hex(String NULL) :: Binary NULL
0 try_inet_aton(String) :: UInt32 NULL
1 try_inet_aton(String NULL) :: UInt32 NULL
0 try_inet_ntoa(Int64) :: String NULL
1 try_inet_ntoa(Int64 NULL) :: String NULL
0 try_json_object FACTORY
0 try_json_object_keep_null FACTORY
0 try_parse_json(Variant) :: Variant NULL
1 try_parse_json(Variant NULL) :: Variant NULL
2 try_parse_json(String) :: Variant NULL
3 try_parse_json(String NULL) :: Variant NULL
0 try_to_binary(Variant) :: Binary NULL
1 try_to_binary(Variant NULL) :: Binary NULL
2 try_to_binary(Bitmap) :: Binary NULL
3 try_to_binary(Bitmap NULL) :: Binary NULL
4 try_to_binary(Geometry) :: Binary NULL
5 try_to_binary(Geometry NULL) :: Binary NULL
6 try_to_binary(Geography) :: Binary NULL
7 try_to_binary(Geography NULL) :: Binary NULL
8 try_to_binary(String) :: Binary NULL
9 try_to_binary(String NULL) :: Binary NULL
0 try_to_boolean(Variant) :: Boolean NULL
1 try_to_boolean(Variant NULL) :: Boolean NULL
2 try_to_boolean(String) :: Boolean NULL
3 try_to_boolean(String NULL) :: Boolean NULL
4 try_to_boolean(UInt8) :: Boolean NULL
5 try_to_boolean(UInt8 NULL) :: Boolean NULL
6 try_to_boolean(UInt16) :: Boolean NULL
7 try_to_boolean(UInt16 NULL) :: Boolean NULL
8 try_to_boolean(UInt32) :: Boolean NULL
9 try_to_boolean(UInt32 NULL) :: Boolean NULL
10 try_to_boolean(UInt64) :: Boolean NULL
11 try_to_boolean(UInt64 NULL) :: Boolean NULL
12 try_to_boolean(Int8) :: Boolean NULL
13 try_to_boolean(Int8 NULL) :: Boolean NULL
14 try_to_boolean(Int16) :: Boolean NULL
15 try_to_boolean(Int16 NULL) :: Boolean NULL
16 try_to_boolean(Int32) :: Boolean NULL
17 try_to_boolean(Int32 NULL) :: Boolean NULL
18 try_to_boolean(Int64) :: Boolean NULL
19 try_to_boolean(Int64 NULL) :: Boolean NULL
20 try_to_boolean(Float32) :: Boolean NULL
21 try_to_boolean(Float32 NULL) :: Boolean NULL
22 try_to_boolean(Float64) :: Boolean NULL
23 try_to_boolean(Float64 NULL) :: Boolean NULL
0 try_to_date(Variant) :: Date NULL
1 try_to_date(Variant NULL) :: Date NULL
2 try_to_date(String, String) :: Date NULL
3 try_to_date(String NULL, String NULL) :: Date NULL
4 try_to_date(String) :: Date NULL
5 try_to_date(String NULL) :: Date NULL
6 try_to_date(Timestamp) :: Date NULL
7 try_to_date(Timestamp NULL) :: Date NULL
8 try_to_date(Int64) :: Date NULL
9 try_to_date(Int64 NULL) :: Date NULL
0 try_to_decimal FACTORY
1 try_to_decimal FACTORY
0 try_to_float32(Variant) :: Float32 NULL
1 try_to_float32(Variant NULL) :: Float32 NULL
2 try_to_float32(String) :: Float32 NULL
3 try_to_float32(String NULL) :: Float32 NULL
4 try_to_float32(UInt8) :: Float32 NULL
5 try_to_float32(UInt8 NULL) :: Float32 NULL
6 try_to_float32(Int8) :: Float32 NULL
7 try_to_float32(Int8 NULL) :: Float32 NULL
8 try_to_float32(UInt16) :: Float32 NULL
9 try_to_float32(UInt16 NULL) :: Float32 NULL
10 try_to_float32(Int16) :: Float32 NULL
11 try_to_float32(Int16 NULL) :: Float32 NULL
12 try_to_float32(UInt32) :: Float32 NULL
13 try_to_float32(UInt32 NULL) :: Float32 NULL
14 try_to_float32(Int32) :: Float32 NULL
15 try_to_float32(Int32 NULL) :: Float32 NULL
16 try_to_float32(UInt64) :: Float32 NULL
17 try_to_float32(UInt64 NULL) :: Float32 NULL
18 try_to_float32(Int64) :: Float32 NULL
19 try_to_float32(Int64 NULL) :: Float32 NULL
20 try_to_float32 FACTORY
21 try_to_float32 FACTORY
22 try_to_float32(Float64) :: Float32 NULL
23 try_to_float32(Float64 NULL) :: Float32 NULL
24 try_to_float32(Boolean) :: Float32 NULL
25 try_to_float32(Boolean NULL) :: Float32 NULL
0 try_to_float64(Variant) :: Float64 NULL
1 try_to_float64(Variant NULL) :: Float64 NULL
2 try_to_float64(String) :: Float64 NULL
3 try_to_float64(String NULL) :: Float64 NULL
4 try_to_float64(UInt8) :: Float64 NULL
5 try_to_float64(UInt8 NULL) :: Float64 NULL
6 try_to_float64(Int8) :: Float64 NULL
7 try_to_float64(Int8 NULL) :: Float64 NULL
8 try_to_float64(UInt16) :: Float64 NULL
9 try_to_float64(UInt16 NULL) :: Float64 NULL
10 try_to_float64(Int16) :: Float64 NULL
11 try_to_float64(Int16 NULL) :: Float64 NULL
12 try_to_float64(UInt32) :: Float64 NULL
13 try_to_float64(UInt32 NULL) :: Float64 NULL
14 try_to_float64(Int32) :: Float64 NULL
15 try_to_float64(Int32 NULL) :: Float64 NULL
16 try_to_float64(UInt64) :: Float64 NULL
17 try_to_float64(UInt64 NULL) :: Float64 NULL
18 try_to_float64(Int64) :: Float64 NULL
19 try_to_float64(Int64 NULL) :: Float64 NULL
20 try_to_float64 FACTORY
21 try_to_float64 FACTORY
22 try_to_float64(Float32) :: Float64 NULL
23 try_to_float64(Float32 NULL) :: Float64 NULL
24 try_to_float64(Boolean) :: Float64 NULL
25 try_to_float64(Boolean NULL) :: Float64 NULL
0 try_to_geometry(Variant) :: Geometry NULL
1 try_to_geometry(Variant NULL) :: Geometry NULL
2 try_to_geometry(Variant, Int32) :: Geometry NULL
3 try_to_geometry(Variant NULL, Int32 NULL) :: Geometry NULL
4 try_to_geometry(String) :: Geometry NULL
5 try_to_geometry(String NULL) :: Geometry NULL
6 try_to_geometry(String, Int32) :: Geometry NULL
7 try_to_geometry(String NULL, Int32 NULL) :: Geometry NULL
8 try_to_geometry(Binary) :: Geometry NULL
9 try_to_geometry(Binary NULL) :: Geometry NULL
10 try_to_geometry(Binary, Int32) :: Geometry NULL
11 try_to_geometry(Binary NULL, Int32 NULL) :: Geometry NULL
0 try_to_int16(Variant) :: Int16 NULL
1 try_to_int16(Variant NULL) :: Int16 NULL
2 try_to_int16(String) :: Int16 NULL
3 try_to_int16(String NULL) :: Int16 NULL
4 try_to_int16(UInt8) :: Int16 NULL
5 try_to_int16(UInt8 NULL) :: Int16 NULL
6 try_to_int16(Int8) :: Int16 NULL
7 try_to_int16(Int8 NULL) :: Int16 NULL
8 try_to_int16(UInt16) :: Int16 NULL
9 try_to_int16(UInt16 NULL) :: Int16 NULL
10 try_to_int16(UInt32) :: Int16 NULL
11 try_to_int16(UInt32 NULL) :: Int16 NULL
12 try_to_int16(Int32) :: Int16 NULL
13 try_to_int16(Int32 NULL) :: Int16 NULL
14 try_to_int16(UInt64) :: Int16 NULL
15 try_to_int16(UInt64 NULL) :: Int16 NULL
16 try_to_int16(Int64) :: Int16 NULL
17 try_to_int16(Int64 NULL) :: Int16 NULL
18 try_to_int16 FACTORY
19 try_to_int16 FACTORY
20 try_to_int16(Float32) :: Int16 NULL
21 try_to_int16(Float32 NULL) :: Int16 NULL
22 try_to_int16(Float64) :: Int16 NULL
23 try_to_int16(Float64 NULL) :: Int16 NULL
24 try_to_int16(Boolean) :: Int16 NULL
25 try_to_int16(Boolean NULL) :: Int16 NULL
0 try_to_int32(Variant) :: Int32 NULL
1 try_to_int32(Variant NULL) :: Int32 NULL
2 try_to_int32(String) :: Int32 NULL
3 try_to_int32(String NULL) :: Int32 NULL
4 try_to_int32(UInt8) :: Int32 NULL
5 try_to_int32(UInt8 NULL) :: Int32 NULL
6 try_to_int32(Int8) :: Int32 NULL
7 try_to_int32(Int8 NULL) :: Int32 NULL
8 try_to_int32(UInt16) :: Int32 NULL
9 try_to_int32(UInt16 NULL) :: Int32 NULL
10 try_to_int32(Int16) :: Int32 NULL
11 try_to_int32(Int16 NULL) :: Int32 NULL
12 try_to_int32(UInt32) :: Int32 NULL
13 try_to_int32(UInt32 NULL) :: Int32 NULL
14 try_to_int32(UInt64) :: Int32 NULL
15 try_to_int32(UInt64 NULL) :: Int32 NULL
16 try_to_int32(Int64) :: Int32 NULL
17 try_to_int32(Int64 NULL) :: Int32 NULL
18 try_to_int32 FACTORY
19 try_to_int32 FACTORY
20 try_to_int32(Float32) :: Int32 NULL
21 try_to_int32(Float32 NULL) :: Int32 NULL
22 try_to_int32(Float64) :: Int32 NULL
23 try_to_int32(Float64 NULL) :: Int32 NULL
24 try_to_int32(Boolean) :: Int32 NULL
25 try_to_int32(Boolean NULL) :: Int32 NULL
0 try_to_int64(Variant) :: Int64 NULL
1 try_to_int64(Variant NULL) :: Int64 NULL
2 try_to_int64(String) :: Int64 NULL
3 try_to_int64(String NULL) :: Int64 NULL
4 try_to_int64(UInt8) :: Int64 NULL
5 try_to_int64(UInt8 NULL) :: Int64 NULL
6 try_to_int64(Int8) :: Int64 NULL
7 try_to_int64(Int8 NULL) :: Int64 NULL
8 try_to_int64(UInt16) :: Int64 NULL
9 try_to_int64(UInt16 NULL) :: Int64 NULL
10 try_to_int64(Int16) :: Int64 NULL
11 try_to_int64(Int16 NULL) :: Int64 NULL
12 try_to_int64(UInt32) :: Int64 NULL
13 try_to_int64(UInt32 NULL) :: Int64 NULL
14 try_to_int64(Int32) :: Int64 NULL
15 try_to_int64(Int32 NULL) :: Int64 NULL
16 try_to_int64(UInt64) :: Int64 NULL
17 try_to_int64(UInt64 NULL) :: Int64 NULL
18 try_to_int64 FACTORY
19 try_to_int64 FACTORY
20 try_to_int64(Float32) :: Int64 NULL
21 try_to_int64(Float32 NULL) :: Int64 NULL
22 try_to_int64(Float64) :: Int64 NULL
23 try_to_int64(Float64 NULL) :: Int64 NULL
24 try_to_int64(Boolean) :: Int64 NULL
25 try_to_int64(Boolean NULL) :: Int64 NULL
26 try_to_int64(Date) :: Int64 NULL
27 try_to_int64(Date NULL) :: Int64 NULL
28 try_to_int64(Timestamp) :: Int64 NULL
29 try_to_int64(Timestamp NULL) :: Int64 NULL
0 try_to_int8(Variant) :: Int8 NULL
1 try_to_int8(Variant NULL) :: Int8 NULL
2 try_to_int8(String) :: Int8 NULL
3 try_to_int8(String NULL) :: Int8 NULL
4 try_to_int8(UInt8) :: Int8 NULL
5 try_to_int8(UInt8 NULL) :: Int8 NULL
6 try_to_int8(UInt16) :: Int8 NULL
7 try_to_int8(UInt16 NULL) :: Int8 NULL
8 try_to_int8(Int16) :: Int8 NULL
9 try_to_int8(Int16 NULL) :: Int8 NULL
10 try_to_int8(UInt32) :: Int8 NULL
11 try_to_int8(UInt32 NULL) :: Int8 NULL
12 try_to_int8(Int32) :: Int8 NULL
13 try_to_int8(Int32 NULL) :: Int8 NULL
14 try_to_int8(UInt64) :: Int8 NULL
15 try_to_int8(UInt64 NULL) :: Int8 NULL
16 try_to_int8(Int64) :: Int8 NULL
17 try_to_int8(Int64 NULL) :: Int8 NULL
18 try_to_int8 FACTORY
19 try_to_int8 FACTORY
20 try_to_int8(Float32) :: Int8 NULL
21 try_to_int8(Float32 NULL) :: Int8 NULL
22 try_to_int8(Float64) :: Int8 NULL
23 try_to_int8(Float64 NULL) :: Int8 NULL
24 try_to_int8(Boolean) :: Int8 NULL
25 try_to_int8(Boolean NULL) :: Int8 NULL
0 try_to_interval(Variant) :: Interval NULL
1 try_to_interval(Variant NULL) :: Interval NULL
2 try_to_interval(String) :: Interval NULL
3 try_to_interval(String NULL) :: Interval NULL
0 try_to_string(Variant) :: String NULL
1 try_to_string(Variant NULL) :: String NULL
2 try_to_string(UInt8) :: String NULL
3 try_to_string(UInt8 NULL) :: String NULL
4 try_to_string(Int8) :: String NULL
5 try_to_string(Int8 NULL) :: String NULL
6 try_to_string(UInt16) :: String NULL
7 try_to_string(UInt16 NULL) :: String NULL
8 try_to_string(Int16) :: String NULL
9 try_to_string(Int16 NULL) :: String NULL
10 try_to_string(UInt32) :: String NULL
11 try_to_string(UInt32 NULL) :: String NULL
12 try_to_string(Int32) :: String NULL
13 try_to_string(Int32 NULL) :: String NULL
14 try_to_string(UInt64) :: String NULL
15 try_to_string(UInt64 NULL) :: String NULL
16 try_to_string(Int64) :: String NULL
17 try_to_string(Int64 NULL) :: String NULL
18 try_to_string(Float32) :: String NULL
19 try_to_string(Float32 NULL) :: String NULL
20 try_to_string(Float64) :: String NULL
21 try_to_string(Float64 NULL) :: String NULL
22 try_to_string(Boolean) :: String NULL
23 try_to_string(Boolean NULL) :: String NULL
24 try_to_string(Date) :: String NULL
25 try_to_string(Date NULL) :: String NULL
26 try_to_string(Timestamp) :: String NULL
27 try_to_string(Timestamp NULL) :: String NULL
28 try_to_string(Binary) :: String NULL
29 try_to_string(Binary NULL) :: String NULL
0 try_to_timestamp(Variant) :: Timestamp NULL
1 try_to_timestamp(Variant NULL) :: Timestamp NULL
2 try_to_timestamp(String) :: Timestamp NULL
3 try_to_timestamp(String NULL) :: Timestamp NULL
4 try_to_timestamp(String, String) :: Timestamp NULL
5 try_to_timestamp(String NULL, String NULL) :: Timestamp NULL
6 try_to_timestamp(Date) :: Timestamp NULL
7 try_to_timestamp(Date NULL) :: Timestamp NULL
8 try_to_timestamp(Int64) :: Timestamp NULL
9 try_to_timestamp(Int64 NULL) :: Timestamp NULL
10 try_to_timestamp(Int64, UInt64) :: Timestamp
11 try_to_timestamp(Int64 NULL, UInt64 NULL) :: Timestamp NULL
0 try_to_uint16(Variant) :: UInt16 NULL
1 try_to_uint16(Variant NULL) :: UInt16 NULL
2 try_to_uint16(String) :: UInt16 NULL
3 try_to_uint16(String NULL) :: UInt16 NULL
4 try_to_uint16(UInt8) :: UInt16 NULL
5 try_to_uint16(UInt8 NULL) :: UInt16 NULL
6 try_to_uint16(Int8) :: UInt16 NULL
7 try_to_uint16(Int8 NULL) :: UInt16 NULL
8 try_to_uint16(Int16) :: UInt16 NULL
9 try_to_uint16(Int16 NULL) :: UInt16 NULL
10 try_to_uint16(UInt32) :: UInt16 NULL
11 try_to_uint16(UInt32 NULL) :: UInt16 NULL
12 try_to_uint16(Int32) :: UInt16 NULL
13 try_to_uint16(Int32 NULL) :: UInt16 NULL
14 try_to_uint16(UInt64) :: UInt16 NULL
15 try_to_uint16(UInt64 NULL) :: UInt16 NULL
16 try_to_uint16(Int64) :: UInt16 NULL
17 try_to_uint16(Int64 NULL) :: UInt16 NULL
18 try_to_uint16 FACTORY
19 try_to_uint16 FACTORY
20 try_to_uint16(Float32) :: UInt16 NULL
21 try_to_uint16(Float32 NULL) :: UInt16 NULL
22 try_to_uint16(Float64) :: UInt16 NULL
23 try_to_uint16(Float64 NULL) :: UInt16 NULL
24 try_to_uint16(Boolean) :: UInt16 NULL
25 try_to_uint16(Boolean NULL) :: UInt16 NULL
0 try_to_uint32(Variant) :: UInt32 NULL
1 try_to_uint32(Variant NULL) :: UInt32 NULL
2 try_to_uint32(String) :: UInt32 NULL
3 try_to_uint32(String NULL) :: UInt32 NULL
4 try_to_uint32(UInt8) :: UInt32 NULL
5 try_to_uint32(UInt8 NULL) :: UInt32 NULL
6 try_to_uint32(Int8) :: UInt32 NULL
7 try_to_uint32(Int8 NULL) :: UInt32 NULL
8 try_to_uint32(UInt16) :: UInt32 NULL
9 try_to_uint32(UInt16 NULL) :: UInt32 NULL
10 try_to_uint32(Int16) :: UInt32 NULL
11 try_to_uint32(Int16 NULL) :: UInt32 NULL
12 try_to_uint32(Int32) :: UInt32 NULL
13 try_to_uint32(Int32 NULL) :: UInt32 NULL
14 try_to_uint32(UInt64) :: UInt32 NULL
15 try_to_uint32(UInt64 NULL) :: UInt32 NULL
16 try_to_uint32(Int64) :: UInt32 NULL
17 try_to_uint32(Int64 NULL) :: UInt32 NULL
18 try_to_uint32 FACTORY
19 try_to_uint32 FACTORY
20 try_to_uint32(Float32) :: UInt32 NULL
21 try_to_uint32(Float32 NULL) :: UInt32 NULL
22 try_to_uint32(Float64) :: UInt32 NULL
23 try_to_uint32(Float64 NULL) :: UInt32 NULL
24 try_to_uint32(Boolean) :: UInt32 NULL
25 try_to_uint32(Boolean NULL) :: UInt32 NULL
0 try_to_uint64(Variant) :: UInt64 NULL
1 try_to_uint64(Variant NULL) :: UInt64 NULL
2 try_to_uint64(String) :: UInt64 NULL
3 try_to_uint64(String NULL) :: UInt64 NULL
4 try_to_uint64(UInt8) :: UInt64 NULL
5 try_to_uint64(UInt8 NULL) :: UInt64 NULL
6 try_to_uint64(Int8) :: UInt64 NULL
7 try_to_uint64(Int8 NULL) :: UInt64 NULL
8 try_to_uint64(UInt16) :: UInt64 NULL
9 try_to_uint64(UInt16 NULL) :: UInt64 NULL
10 try_to_uint64(Int16) :: UInt64 NULL
11 try_to_uint64(Int16 NULL) :: UInt64 NULL
12 try_to_uint64(UInt32) :: UInt64 NULL
13 try_to_uint64(UInt32 NULL) :: UInt64 NULL
14 try_to_uint64(Int32) :: UInt64 NULL
15 try_to_uint64(Int32 NULL) :: UInt64 NULL
16 try_to_uint64(Int64) :: UInt64 NULL
17 try_to_uint64(Int64 NULL) :: UInt64 NULL
18 try_to_uint64 FACTORY
19 try_to_uint64 FACTORY
20 try_to_uint64(Float32) :: UInt64 NULL
21 try_to_uint64(Float32 NULL) :: UInt64 NULL
22 try_to_uint64(Float64) :: UInt64 NULL
23 try_to_uint64(Float64 NULL) :: UInt64 NULL
24 try_to_uint64(Boolean) :: UInt64 NULL
25 try_to_uint64(Boolean NULL) :: UInt64 NULL
0 try_to_uint8(Variant) :: UInt8 NULL
1 try_to_uint8(Variant NULL) :: UInt8 NULL
2 try_to_uint8(String) :: UInt8 NULL
3 try_to_uint8(String NULL) :: UInt8 NULL
4 try_to_uint8(Int8) :: UInt8 NULL
5 try_to_uint8(Int8 NULL) :: UInt8 NULL
6 try_to_uint8(UInt16) :: UInt8 NULL
7 try_to_uint8(UInt16 NULL) :: UInt8 NULL
8 try_to_uint8(Int16) :: UInt8 NULL
9 try_to_uint8(Int16 NULL) :: UInt8 NULL
10 try_to_uint8(UInt32) :: UInt8 NULL
11 try_to_uint8(UInt32 NULL) :: UInt8 NULL
12 try_to_uint8(Int32) :: UInt8 NULL
13 try_to_uint8(Int32 NULL) :: UInt8 NULL
14 try_to_uint8(UInt64) :: UInt8 NULL
15 try_to_uint8(UInt64 NULL) :: UInt8 NULL
16 try_to_uint8(Int64) :: UInt8 NULL
17 try_to_uint8(Int64 NULL) :: UInt8 NULL
18 try_to_uint8 FACTORY
19 try_to_uint8 FACTORY
20 try_to_uint8(Float32) :: UInt8 NULL
21 try_to_uint8(Float32 NULL) :: UInt8 NULL
22 try_to_uint8(Float64) :: UInt8 NULL
23 try_to_uint8(Float64 NULL) :: UInt8 NULL
24 try_to_uint8(Boolean) :: UInt8 NULL
25 try_to_uint8(Boolean NULL) :: UInt8 NULL
0 try_to_variant(T0) :: Variant NULL
1 try_to_variant(T0 NULL) :: Variant NULL
0 tuple FACTORY
0 typeof(Variant) :: String
1 typeof(Variant NULL) :: String NULL
2 typeof(T0) :: String
0 unicode(String) :: UInt32
1 unicode(String NULL) :: UInt32 NULL
0 unnest FACTORY
0 upper(String) :: String
1 upper(String NULL) :: String NULL
0 xor(Boolean, Boolean) :: Boolean
1 xor(Boolean NULL, Boolean NULL) :: Boolean NULL
0 xxhash32(Variant) :: UInt32
1 xxhash32(Variant NULL) :: UInt32 NULL
2 xxhash32(String) :: UInt32
3 xxhash32(String NULL) :: UInt32 NULL
4 xxhash32(Date) :: UInt32
5 xxhash32(Date NULL) :: UInt32 NULL
6 xxhash32(Timestamp) :: UInt32
7 xxhash32(Timestamp NULL) :: UInt32 NULL
8 xxhash32(Boolean) :: UInt32
9 xxhash32(Boolean NULL) :: UInt32 NULL
10 xxhash32(Bitmap) :: UInt32
11 xxhash32(Bitmap NULL) :: UInt32 NULL
12 xxhash32(UInt8) :: UInt32
13 xxhash32(UInt8 NULL) :: UInt32 NULL
14 xxhash32(Int8) :: UInt32
15 xxhash32(Int8 NULL) :: UInt32 NULL
16 xxhash32(UInt16) :: UInt32
17 xxhash32(UInt16 NULL) :: UInt32 NULL
18 xxhash32(Int16) :: UInt32
19 xxhash32(Int16 NULL) :: UInt32 NULL
20 xxhash32(UInt32) :: UInt32
21 xxhash32(UInt32 NULL) :: UInt32 NULL
22 xxhash32(Int32) :: UInt32
23 xxhash32(Int32 NULL) :: UInt32 NULL
24 xxhash32(UInt64) :: UInt32
25 xxhash32(UInt64 NULL) :: UInt32 NULL
26 xxhash32(Int64) :: UInt32
27 xxhash32(Int64 NULL) :: UInt32 NULL
28 xxhash32 FACTORY
29 xxhash32(Float32) :: UInt32
30 xxhash32(Float32 NULL) :: UInt32 NULL
31 xxhash32(Float64) :: UInt32
32 xxhash32(Float64 NULL) :: UInt32 NULL
0 xxhash64(Variant) :: UInt64
1 xxhash64(Variant NULL) :: UInt64 NULL
2 xxhash64(String) :: UInt64
3 xxhash64(String NULL) :: UInt64 NULL
4 xxhash64(Date) :: UInt64
5 xxhash64(Date NULL) :: UInt64 NULL
6 xxhash64(Timestamp) :: UInt64
7 xxhash64(Timestamp NULL) :: UInt64 NULL
8 xxhash64(Boolean) :: UInt64
9 xxhash64(Boolean NULL) :: UInt64 NULL
10 xxhash64(Bitmap) :: UInt64
11 xxhash64(Bitmap NULL) :: UInt64 NULL
12 xxhash64(UInt8) :: UInt64
13 xxhash64(UInt8 NULL) :: UInt64 NULL
14 xxhash64(Int8) :: UInt64
15 xxhash64(Int8 NULL) :: UInt64 NULL
16 xxhash64(UInt16) :: UInt64
17 xxhash64(UInt16 NULL) :: UInt64 NULL
18 xxhash64(Int16) :: UInt64
19 xxhash64(Int16 NULL) :: UInt64 NULL
20 xxhash64(UInt32) :: UInt64
21 xxhash64(UInt32 NULL) :: UInt64 NULL
22 xxhash64(Int32) :: UInt64
23 xxhash64(Int32 NULL) :: UInt64 NULL
24 xxhash64(UInt64) :: UInt64
25 xxhash64(UInt64 NULL) :: UInt64 NULL
26 xxhash64(Int64) :: UInt64
27 xxhash64(Int64 NULL) :: UInt64 NULL
28 xxhash64 FACTORY
29 xxhash64(Float32) :: UInt64
30 xxhash64(Float32 NULL) :: UInt64 NULL
31 xxhash64(Float64) :: UInt64
32 xxhash64(Float64 NULL) :: UInt64 NULL
0 yearweek(Date) :: UInt32
1 yearweek(Date NULL) :: UInt32 NULL
2 yearweek(Timestamp) :: UInt32
3 yearweek(Timestamp NULL) :: UInt32 NULL
0 yesterday() :: Date
