ast            : l_extendedprice + (1 - l_discount) - l_quantity
raw expr       : minus(plus(l_extendedprice::Decimal(15, 2), minus(1, l_discount::Decimal(15, 2))), l_quantity::Decimal(15, 2))
checked expr   : minus<Decimal(17, 2), Decimal(15, 2)>(plus<Decimal(15, 2), Decimal(16, 2)>(l_extendedprice, minus<UInt8, Decimal(15, 2)>(1_u8, l_discount)), l_quantity)
evaluation:
+--------+-----------------------+----------------+-----------------+-----------------------+
|        | l_extendedprice       | l_discount     | l_quantity      | Output                |
+--------+-----------------------+----------------+-----------------+-----------------------+
| Type   | Decimal(15, 2)        | Decimal(15, 2) | Decimal(15, 2)  | Decimal(18, 2)        |
| Domain | {19411.84..=85768.32} | {0.00..=0.09}  | {16.00..=48.00} | {19364.75..=85753.32} |
| Row 0  | 70290.81              | 0.08           | 43.00           | 70248.73              |
| Row 1  | 39880.40              | 0.08           | 40.00           | 39841.32              |
| Row 2  | 26960.83              | 0.00           | 23.00           | 26938.83              |
| Row 3  | 85768.32              | 0.09           | 48.00           | 85721.23              |
| Row 4  | 31790.20              | 0.07           | 20.00           | 31771.13              |
| Row 5  | 19411.84              | 0.07           | 16.00           | 19396.77              |
| Row 6  | 69118.22              | 0.07           | 46.00           | 69073.15              |
| Row 7  | 48748.25              | 0.09           | 25.00           | 48724.16              |
| Row 8  | 42072.70              | 0.09           | 37.00           | 42036.61              |
| Row 9  | 44254.44              | 0.02           | 36.00           | 44219.42              |
+--------+-----------------------+----------------+-----------------+-----------------------+
evaluation (internal):
+-----------------+------------------------------------------------------------------------------------------------------------------+
| Column          | Data                                                                                                             |
+-----------------+------------------------------------------------------------------------------------------------------------------+
| l_extendedprice | Decimal64([70290.81, 39880.40, 26960.83, 85768.32, 31790.20, 19411.84, 69118.22, 48748.25, 42072.70, 44254.44])  |
| l_discount      | Decimal64([0.08, 0.08, 0.00, 0.09, 0.07, 0.07, 0.07, 0.09, 0.09, 0.02])                                          |
| l_quantity      | Decimal64([43.00, 40.00, 23.00, 48.00, 20.00, 16.00, 46.00, 25.00, 37.00, 36.00])                                |
| Output          | Decimal128([70248.73, 39841.32, 26938.83, 85721.23, 31771.13, 19396.77, 69073.15, 48724.16, 42036.61, 44219.42]) |
+-----------------+------------------------------------------------------------------------------------------------------------------+


ast            : 1964831797.0000 - 0.0214642400000
raw expr       : minus(1964831797.0000, 0.0214642400000)
checked expr   : minus<Decimal(14, 4), Decimal(13, 13)>(1964831797.0000_d128(14,4), 0.0214642400000_d128(13,13))
optimized expr : 1964831796.9785357600000_d128(24,13)
output type    : Decimal(24, 13)
output domain  : {1964831796.9785357600000..=1964831796.9785357600000}
output         : 1964831796.9785357600000


