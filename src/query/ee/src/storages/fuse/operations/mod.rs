// Copyright 2023 Databend Cloud
//
// Licensed under the Elastic License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.elastic.co/licensing/elastic-license
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

pub mod handler;
pub mod ngram_index;
pub mod vacuum_drop_tables;
pub mod vacuum_table;
pub mod vacuum_table_v2;
pub mod vacuum_temporary_files;
pub mod virtual_columns;
pub use handler::RealVacuumHandler;
