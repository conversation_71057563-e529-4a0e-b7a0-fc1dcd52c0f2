[package]
name = "databend-enterprise-table-index"
description = "table index handler, include inverted index, ngram index and vector index"
version = { workspace = true }
authors = { workspace = true }
license = { workspace = true }
publish = { workspace = true }
edition = { workspace = true }

[dependencies]
async-backtrace = { workspace = true }
async-trait = { workspace = true }
databend-common-base = { workspace = true }
databend-common-catalog = { workspace = true }
databend-common-exception = { workspace = true }
databend-common-expression = { workspace = true }
databend-common-meta-app = { workspace = true }
databend-common-pipeline-core = { workspace = true }
databend-common-storages-fuse = { workspace = true }
databend-storages-common-table-meta = { workspace = true }

[build-dependencies]

[lints]
workspace = true
